import { getPublicAssets } from '@/services/public-assets-service';
import { getSiteParameters } from '@/services/site-parameters-service';
import { NextIntlClientProvider, useLocale } from 'next-intl';

import { LayoutWrapper } from '@/components/layout-wrapper';
import { WebStats } from '@/components/web-stats';

import { themeToStyle } from '@iprox/react-ui/server';

import { timeZone } from '../../i18n';
import { AppSettingsProvider } from '../_services/settings.context';
import { generateFontFaceRules } from '../_utils/fonts-mapper';
import ConsentBanner from '@/components/consent-banner';
import { getConsent } from '@/services/consent-service';

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const locale = useLocale();
  const siteParameters = await getSiteParameters();
  const publicAssets = await getPublicAssets();

  const cssVariablesString = themeToStyle(siteParameters?.siteParametersSettings.cssVariables ?? []);

  const { fontFaces, styleVariables } = generateFontFaceRules(
    publicAssets?.siteAssets.fonts ?? [],
    process.env.IPROX_OPEN_API_URL ?? ''
  );

  const messages =
    locale === 'en'
      ? {
          ...(await import('@iprox/react-ui-i18n/en.json')).default,
          ...(await import('../../i18n/en.json')).default,
        }
      : {
          ...(await import('@iprox/react-ui-i18n/nl.json')).default,
          ...(await import('../../i18n/nl.json')).default,
        };

    const consent = typeof window !== 'undefined' ? getConsent() : null;

  return (
    <html lang={locale}>
      <head>
        {publicAssets?.siteAssets?.favicon ? (
          <link rel="icon" href={`${process.env.IPROX_OPEN_API_URL}/asset${publicAssets.siteAssets.favicon}`} />
        ) : null}
        <style>
          {`
            :root {
              ${cssVariablesString.concat(styleVariables).join('\n')}
            }
            ${fontFaces.join('\n')}
          `}
        </style>
      </head>
      <body className="flex min-h-screen flex-col">
        <ConsentBanner />
        <WebStats consew/>
        <AppSettingsProvider
          settings={{
            apiUrl: process.env.IPROX_OPEN_API_URL || '',
          }}
        >
          <NextIntlClientProvider locale={locale} messages={messages} timeZone={timeZone}>
            <LayoutWrapper logoAssetPath={publicAssets?.siteAssets?.logo}>{children}</LayoutWrapper>
          </NextIntlClientProvider>
        </AppSettingsProvider>
      </body>
    </html>
  );
}
