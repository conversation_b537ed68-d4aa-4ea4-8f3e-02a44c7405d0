import { useState, useEffect } from 'react';

import { getConsent, setConsent } from '@/services/consent-service';

export default function ConsentBanner() {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (!getConsent()) {
      setShow(true);
    }
  }, []);

  const accept = () => {
    setConsent('accepted');
    setShow(false);
    window.location.reload(); // force reload to re-inject scripts
  };

  if (!show) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-800 text-white p-4 z-50">
      <div className="flex justify-between items-center">
        <p>We gebruiken cookies voor analytics. Akkoord?</p>
        <button onClick={accept} className="ml-4 bg-green-500 px-4 py-2 rounded">
          Ja, akkoord
        </button>
      </div>
    </div>
  );
}
