import { getWebStatsSettings } from '@/services/settings-service';
import Script from 'next/script';

let webStatsSettings: { type?: string; key?: string; domain?: string } | undefined = undefined;
let webStatsSettingsPromise: Promise<{ type?: string; key?: string; domain?: string } | undefined> | undefined = undefined;

async function GetSettings(): Promise<{ type?: string; key?: string; domain?: string } | undefined> {
  if (webStatsSettings) {
    return webStatsSettings;
  }

  if (!webStatsSettingsPromise) {
    webStatsSettingsPromise = getWebStatsSettings().then((result) => {
      webStatsSettings = result?.webStatsSettings;
      return webStatsSettings;
    });
  }

  return webStatsSettingsPromise;
}

export async function WebStatsCsps() {
  const settings = await GetSettings();
  if (!settings) {
    return null;
  }

  const { type, key, domain } = settings;

  if (!key || !type) return null;

  switch (type) {
    case 'Google':
      return {
        'script-src': 'https://www.google-analytics.com',
      };
    case 'Matomo':
      return {
        'connect-src': `https://${domain}.matomo.cloud`,
        'script-src': 'https://cdn.matomo.cloud',
      };
    case 'PiwikPro':
      return {
        'connect-src': domain,
        'script-src': domain,
      };
    case 'Siteimprove':
      return {
        'img-src': `https://${key}.global.siteimproveanalytics.io`,
        'script-src': 'https://siteimproveanalytics.com',
      };
  }

  return null;
}

export async function WebStats() {
  const settings = await GetSettings();
  if (!settings) {
    return;
  }

  const { type, key, domain } = settings;
  if (!key || !type) return null;

  switch (type) {
    case 'Google':
      return (
        <>
          <Script src={`https://www.google-analytics.com/gtag/js?id=${key}`} strategy="afterInteractive" />
          <Script id="gtag-init" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${key}');
            `}
          </Script>
        </>
      );

    case 'Matomo':
      return (
        <Script id="matomo-init" strategy="afterInteractive">
          {`
            var _paq = window._paq = window._paq || [];
            _paq.push(['trackPageView']);
            _paq.push(['enableLinkTracking']);
            (function() {
              var u="https://${domain}.matomo.cloud/";
              _paq.push(['setTrackerUrl', u+'matomo.php']);
              _paq.push(['setSiteId', '${key}']);
              var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
              g.async=true; g.src='https://cdn.matomo.cloud/${domain}.matomo.cloud/matomo.js'; s.parentNode.insertBefore(g,s);
            })();
          `}
        </Script>
      );

    case 'PiwikPro':
      return (
        <Script id="piwik-init" strategy="afterInteractive">
          {`
            var _paq = window._paq = window._paq || [];
            _paq.push(['trackPageView']);
            _paq.push(['enableLinkTracking']);
            (function() {
              var u="${domain}";
              _paq.push(['setTrackerUrl', u+'piwik.php']);
              _paq.push(['setSiteId', '${key}']);
              var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
              g.async=true; g.src=u+'piwik.js'; s.parentNode.insertBefore(g,s);
            })();
          `}
        </Script>
      );

    case 'Siteimprove':
      return (
        <Script id="siteimprove-init" strategy="afterInteractive">
          {`
            (function() {
              var sz = document.createElement('script'); sz.type = 'text/javascript'; sz.async = true;
              sz.src = 'https://siteimproveanalytics.com/js/siteanalyze_${key}.js';
              var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(sz, s);
            })();
          `}
        </Script>
      );

    default:
      return null;
  }
}
