import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { HTTPError } from 'ky';

export async function getSecuritySettings(): Promise<components['schemas']['GetSecuritySettingsResponse'] | undefined> {
  try {
    const securitySettings = await serverApi
      .get(`settings/security`)
      .json<components['schemas']['GetSecuritySettingsResponse']>();
    return securitySettings;
  } catch (error) {
    if (error instanceof HTTPError && error.response.status === 404) {
      return undefined;
    }
    return Promise.reject(error);
  }
}
