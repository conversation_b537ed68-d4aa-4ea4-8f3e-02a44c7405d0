/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

/** WithRequired type helpers */
type WithRequired<T, K extends keyof T> = T & { [P in K]-?: T[P] };

export interface paths {
  '/api/v1/dossier/{id}/latest-published-version': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetLatestPublishedDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/{id}/latest-version': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetLatestDossierVersionResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/{id}/versions': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetDossierAllVersionsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/dossier-entry/{entryId}': {
    get: {
      parameters: {
        path: {
          entryId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetDossierEntryResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier': {
    /** Update Dossier */
    put: {
      /**
       * @description 1. DossierId: Is Required
       * 2. Summary: Should be the length between 0 and 1000, System.ComponentModel.DataAnnotations.DisplayAttribute
       * 3. DynamicFieldValues: Iprox.Open.Application.Attributes.DossierDynamicFieldValidationAttribute
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateDossierCommand'];
          'application/json': components['schemas']['UpdateDossierCommand'];
          'text/json': components['schemas']['UpdateDossierCommand'];
          'application/*+json': components['schemas']['UpdateDossierCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    /** Create Dossier */
    post: {
      /**
       * @description 1. Title: Is Required, Should be the length between 1 and 255
       * 2. CategoryId: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['CreateDossierCommand'];
          'application/json': components['schemas']['CreateDossierCommand'];
          'text/json': components['schemas']['CreateDossierCommand'];
          'application/*+json': components['schemas']['CreateDossierCommand'];
        };
      };
      responses: {
        /** @description Created */
        201: {
          content: {
            'application/json': components['schemas']['CreateDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    /** Delete Dossier */
    delete: {
      /** @description 1. DossierId: Is Required */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['DeleteDossierCommand'];
          'application/json': components['schemas']['DeleteDossierCommand'];
          'text/json': components['schemas']['DeleteDossierCommand'];
          'application/*+json': components['schemas']['DeleteDossierCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/dossier-title': {
    /** Update Dossier Title */
    put: {
      /**
       * @description 1. DossierId: Is Required
       * 2. Title: Is Required, Should be the length between 1 and 255
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateDossierTitleCommand'];
          'application/json': components['schemas']['UpdateDossierTitleCommand'];
          'text/json': components['schemas']['UpdateDossierTitleCommand'];
          'application/*+json': components['schemas']['UpdateDossierTitleCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateDossierTitleResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/{id}/image': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/octet-stream':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    put: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
      };
    };
    delete: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/publish-dossier': {
    /** Publish Dossier */
    post: {
      /** @description 1. DossierId: Is Required */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['PublishDossierCommand'];
          'application/json': components['schemas']['PublishDossierCommand'];
          'text/json': components['schemas']['PublishDossierCommand'];
          'application/*+json': components['schemas']['PublishDossierCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['PublishDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/unpublish-dossier': {
    /** UnPublish Dossier */
    post: {
      /** @description 1. DossierId: Is Required, Should be a Guid */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UnpublishDossierCommand'];
          'application/json': components['schemas']['UnpublishDossierCommand'];
          'text/json': components['schemas']['UnpublishDossierCommand'];
          'application/*+json': components['schemas']['UnpublishDossierCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UnpublishDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/upload-zipped-dossier/{id}': {
    post: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UploadDossierZipResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/upload-and-publish-sharepoint-files/{id}': {
    /** Upload and Publish Dossier SharePoint Files */
    post: {
      parameters: {
        path: {
          id: string;
        };
      };
      /**
       * @description 1. SiteId: Is Required
       * 2. ListId: Is Required
       * 3. SelectedFiles: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UploadAndPublishDossierSharePointFilesCommand'];
          'application/json': components['schemas']['UploadAndPublishDossierSharePointFilesCommand'];
          'text/json': components['schemas']['UploadAndPublishDossierSharePointFilesCommand'];
          'application/*+json': components['schemas']['UploadAndPublishDossierSharePointFilesCommand'];
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/{id}/upload-file': {
    post: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
      };
    };
  };
  '/api/v1/dossier/{id}/folder/{folderId}/upload-file': {
    post: {
      parameters: {
        path: {
          id: string;
          folderId: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
      };
    };
  };
  '/api/v1/dossier/paged': {
    get: {
      parameters: {
        query?: {
          Count?: number;
          Start?: number;
          OrderBy?: string;
          Filter?: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetPagedDossierResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier/dynamic-fields': {
    patch: {
      parameters: {
        query?: {
          categoryId?: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateDossierDynamicFieldsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-category/{id}': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetDossierCategoryResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-category': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetAllDossierCategoriesResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    put: {
      /**
       * @description 1. DossierCategoryId: Is Required
       * 2. Alias: Is Required, Should be the length between 5 and 100
       * 3. Label: Is Required, Should be the length between 5 and 100
       * 4. RequirePublishDates: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateDossierCategoryCommand'];
          'application/json': components['schemas']['UpdateDossierCategoryCommand'];
          'text/json': components['schemas']['UpdateDossierCategoryCommand'];
          'application/*+json': components['schemas']['UpdateDossierCategoryCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateDossierCategoryResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    post: {
      /**
       * @description 1. Alias: Is Required, Should be the length between 5 and 100
       * 2. Label: Is Required, Should be the length between 5 and 100
       * 3. RequirePublishDates: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['CreateDossierCategoryCommand'];
          'application/json': components['schemas']['CreateDossierCategoryCommand'];
          'text/json': components['schemas']['CreateDossierCategoryCommand'];
          'application/*+json': components['schemas']['CreateDossierCategoryCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['CreateDossierCategoryResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    delete: {
      parameters: {
        query?: {
          id?: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteDossierCategoryResponse'];
          };
        };
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-category/{dossierCategoryId}/dynamic-fields': {
    get: {
      parameters: {
        path: {
          dossierCategoryId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetDossierCategoryDynamicFieldsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    put: {
      parameters: {
        path: {
          dossierCategoryId: string;
        };
      };
      /** @description 1. DynamicFields: System.ComponentModel.DataAnnotations.MinLengthAttribute */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['DossierCategoryUpdateDynamicFieldsCommand'];
          'application/json': components['schemas']['DossierCategoryUpdateDynamicFieldsCommand'];
          'text/json': components['schemas']['DossierCategoryUpdateDynamicFieldsCommand'];
          'application/*+json': components['schemas']['DossierCategoryUpdateDynamicFieldsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateDossierCategoryDynamicFieldsResponse'];
          };
        };
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-category/{dossierCategoryId}/dynamic-field': {
    /** @deprecated */
    post: {
      parameters: {
        path: {
          dossierCategoryId: string;
        };
      };
      /**
       * @description 1. DossierCategoryId: Is Required
       * 2. DynamicFieldId: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['DossierCategoryAddDynamicFieldCommand'];
          'application/json': components['schemas']['DossierCategoryAddDynamicFieldCommand'];
          'text/json': components['schemas']['DossierCategoryAddDynamicFieldCommand'];
          'application/*+json': components['schemas']['DossierCategoryAddDynamicFieldCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['AddDossierCategoryDynamicFieldResponse'];
          };
        };
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    /** @deprecated */
    delete: {
      parameters: {
        query?: {
          id?: string;
        };
        path: {
          dossierCategoryId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteDossierCategoryDynamicFieldResponse'];
          };
        };
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-file-structure/{dossierId}/top-level-nodes': {
    get: {
      parameters: {
        path: {
          dossierId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetTopLevelNodesResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-file-structure/{folderId}/node-children': {
    get: {
      parameters: {
        path: {
          folderId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetNodeChildrenResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-file-structure/{folderId}/paged': {
    get: {
      parameters: {
        query?: {
          Count?: number;
          Start?: number;
        };
        path: {
          folderId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetPagedNodeChildrenResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-file-structure/dossier-metrics': {
    get: {
      parameters: {
        query?: {
          Filter?: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetDossierMetricsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-file-structure/{dossierId}/nodes': {
    /** Delete Dossier Files/Folders */
    delete: {
      parameters: {
        path: {
          dossierId: string;
        };
      };
      /** @description 1. NodeIds: Is Required */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['DeleteFileFolderNodesCommand'];
          'application/json': components['schemas']['DeleteFileFolderNodesCommand'];
          'text/json': components['schemas']['DeleteFileFolderNodesCommand'];
          'application/*+json': components['schemas']['DeleteFileFolderNodesCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteFileFolderNodesResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-file-structure/{nodeId}/name': {
    /** Rename File/Folder */
    patch: {
      parameters: {
        path: {
          nodeId: string;
        };
      };
      /** @description 1. Name: Is Required, Contains invalid characters, Should be the length between 1 and 256 */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['RenameFileFolderNodeCommand'];
          'application/json': components['schemas']['RenameFileFolderNodeCommand'];
          'text/json': components['schemas']['RenameFileFolderNodeCommand'];
          'application/*+json': components['schemas']['RenameFileFolderNodeCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dossier-file-structure/{folderId}/folder': {
    /** Create Folder */
    post: {
      parameters: {
        path: {
          folderId: string;
        };
      };
      /** @description 1. Name: Contains invalid characters, Should be the length between 1 and 255 */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['CreateFolderCommand'];
          'application/json': components['schemas']['CreateFolderCommand'];
          'text/json': components['schemas']['CreateFolderCommand'];
          'application/*+json': components['schemas']['CreateFolderCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['CreateFolderResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/download/token/{nodeId}': {
    get: {
      parameters: {
        path: {
          nodeId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'text/plain': components['schemas']['DownloadTokenResponse'];
            'application/json': components['schemas']['DownloadTokenResponse'];
            'text/json': components['schemas']['DownloadTokenResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'text/plain':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
            'text/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/download/{token}': {
    get: {
      parameters: {
        query?: {
          IpAddress?: components['schemas']['IPAddress'] | components['schemas']['ReadOnlyIPAddress'];
        };
        path: {
          token: string;
        };
      };
      /** @description 1. DownloadNodeId: Should be a Guid */
      requestBody?: {
        content: {
          'multipart/form-data': {
            'Response.HttpContext.Request.Form'?: components['schemas']['StringStringValuesKeyValuePair'][];
          };
        };
      };
      responses: {
        /** @description Success */
        200: never;
      };
    };
  };
  '/api/v1/dynamic-field/{id}': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetDynamicFieldResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dynamic-field': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetAllDynamicFieldResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    put: {
      /**
       * @description 1. DynamicFieldId: Is Required
       * 2. Alias: Is Required, Should be the length between 5 and 100
       * 3. Label: Is Required, Should be the length between 5 and 100
       * 4. DynamicFieldType: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateDynamicFieldCommand'];
          'application/json': components['schemas']['UpdateDynamicFieldCommand'];
          'text/json': components['schemas']['UpdateDynamicFieldCommand'];
          'application/*+json': components['schemas']['UpdateDynamicFieldCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateDynamicFieldResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    post: {
      /**
       * @description 1. Alias: Is Required, Should be the length between 5 and 100
       * 2. Label: Is Required, Should be the length between 5 and 100
       * 3. DynamicFieldType: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['CreateDynamicFieldCommand'];
          'application/json': components['schemas']['CreateDynamicFieldCommand'];
          'text/json': components['schemas']['CreateDynamicFieldCommand'];
          'application/*+json': components['schemas']['CreateDynamicFieldCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['CreateDynamicFieldResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    delete: {
      parameters: {
        query?: {
          id?: string;
        };
      };
      responses: {
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dynamic-field/{dynamicFieldId}/validation-rules': {
    post: {
      parameters: {
        path: {
          dynamicFieldId: string;
        };
      };
      /**
       * @description 1. DynamicFieldId: Is Required, Should be a Guid
       * 2. ValidationRules: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateDynamicFieldValidationRulesCommand'];
          'application/json': components['schemas']['UpdateDynamicFieldValidationRulesCommand'];
          'text/json': components['schemas']['UpdateDynamicFieldValidationRulesCommand'];
          'application/*+json': components['schemas']['UpdateDynamicFieldValidationRulesCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateDynamicFieldValidationRulesResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/dynamic-field/{dynamicFieldId}/validation-rule': {
    put: {
      parameters: {
        path: {
          dynamicFieldId: string;
        };
      };
      /**
       * @description 1. DynamicFieldId: Is Required, Should be a Guid
       * 2. ValidationRuleType: Is Required
       * 3. FieldRuleValue: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateDynamicFieldValidationRuleCommand'];
          'application/json': components['schemas']['UpdateDynamicFieldValidationRuleCommand'];
          'text/json': components['schemas']['UpdateDynamicFieldValidationRuleCommand'];
          'application/*+json': components['schemas']['UpdateDynamicFieldValidationRuleCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteDynamicFieldValidationRuleCommand'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    post: {
      parameters: {
        path: {
          dynamicFieldId: string;
        };
      };
      /**
       * @description 1. DynamicFieldId: Is Required, Should be a Guid
       * 2. ValidationRuleType: Is Required
       * 3. FieldRuleValue: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['AddDynamicFieldValidationRuleCommand'];
          'application/json': components['schemas']['AddDynamicFieldValidationRuleCommand'];
          'text/json': components['schemas']['AddDynamicFieldValidationRuleCommand'];
          'application/*+json': components['schemas']['AddDynamicFieldValidationRuleCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['AddDossierCategoryDynamicFieldResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    delete: {
      parameters: {
        query?: {
          validationRuleType?:
            | 'ValidateGuid'
            | 'RequiredProperty'
            | 'ValidateNumberRange'
            | 'ValidateStringLength'
            | 'ValidateDate'
            | 'ValidatePastDate'
            | 'ValidateFutureDate'
            | 'ValidateIpAddress'
            | 'ValidateDateTimeRange';
        };
        path: {
          dynamicFieldId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteDynamicFieldValidationRuleCommand'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/file-node/dynamic-fields': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetFileNodeDynamicFieldsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    put: {
      /** @description 1. DynamicFields: System.ComponentModel.DataAnnotations.MinLengthAttribute */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['FileNodeUpdateDynamicFieldsCommand'];
          'application/json': components['schemas']['FileNodeUpdateDynamicFieldsCommand'];
          'text/json': components['schemas']['FileNodeUpdateDynamicFieldsCommand'];
          'application/*+json': components['schemas']['FileNodeUpdateDynamicFieldsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateFileNodeDynamicFieldsResponse'];
          };
        };
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/font-asset/{id}': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetFontAssetResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/font-asset': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetAllFontAssetsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    delete: {
      /** @description 1. FontAssetId: Is Required */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['DeleteFontAssetCommand'];
          'application/json': components['schemas']['DeleteFontAssetCommand'];
          'text/json': components['schemas']['DeleteFontAssetCommand'];
          'application/*+json': components['schemas']['DeleteFontAssetCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteFontAssetResponse'];
          };
        };
        /** @description Not Found */
        404: {
          content: {
            'application/json': components['schemas']['NotFoundResult'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/font-asset/create': {
    post: {
      /**
       * @description 1. FontCategory: Is Required
       * 2. FontFamily: Is Required
       * 3. FontWeight: Is Required
       * 4. FontStyle: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['CreateFontAssetCommand'];
          'application/json': components['schemas']['CreateFontAssetCommand'];
          'text/json': components['schemas']['CreateFontAssetCommand'];
          'application/*+json': components['schemas']['CreateFontAssetCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Created */
        201: {
          content: {
            'application/json': components['schemas']['CreateFontAssetResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/font-asset/update': {
    put: {
      /**
       * @description 1. FontAssetId: Is Required
       * 2. FontFamily: Is Required
       * 3. FontCategory: Is Required
       * 4. FontWeight: Is Required
       * 5. FontStyle: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateFontAssetCommand'];
          'application/json': components['schemas']['UpdateFontAssetCommand'];
          'text/json': components['schemas']['UpdateFontAssetCommand'];
          'application/*+json': components['schemas']['UpdateFontAssetCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['UpdateFontAssetResponse'];
          };
        };
        /** @description Not Found */
        404: {
          content: {
            'application/json': components['schemas']['NotFoundResult'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/font-asset/upload-fonts/{fontAssetId}': {
    post: {
      parameters: {
        path: {
          fontAssetId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetFontAssetUploadedFontsResponse'];
          };
        };
        /** @description Not Found */
        404: {
          content: {
            'application/json': components['schemas']['NotFoundResult'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/font-asset/fonts': {
    delete: {
      /**
       * @description 1. FontAssetId: Is Required
       * 2. FontIds: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['DeleteFontsCommand'];
          'application/json': components['schemas']['DeleteFontsCommand'];
          'text/json': components['schemas']['DeleteFontsCommand'];
          'application/*+json': components['schemas']['DeleteFontsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['DeleteFontsResponse'];
          };
        };
        /** @description Not Found */
        404: {
          content: {
            'application/json': components['schemas']['NotFoundResult'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/image-asset/logo': {
    put: {
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/image-asset/favicon': {
    put: {
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/image-asset/home-page-image': {
    put: {
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/component/footer': {
    /** Update Footer Component */
    put: {
      /**
       * @description 1. Label: Is Required
       * 2. Slug: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateFooterComponentCommand'];
          'application/json': components['schemas']['UpdateFooterComponentCommand'];
          'text/json': components['schemas']['UpdateFooterComponentCommand'];
          'application/*+json': components['schemas']['UpdateFooterComponentCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetPageListResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/{id}': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json':
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/paged': {
    get: {
      parameters: {
        query?: {
          Count?: number;
          Start?: number;
          OrderBy?: string;
          Filter?: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetPagedPagesResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/home-page': {
    /** Update Home Page */
    put: {
      /**
       * @description 1. Label: Is Required
       * 2. Slug: Is Required
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateHomePageCommand'];
          'application/json': components['schemas']['UpdateHomePageCommand'];
          'text/json': components['schemas']['UpdateHomePageCommand'];
          'application/*+json': components['schemas']['UpdateHomePageCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/navigation-structure': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetNavigationStructureResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/search-page': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': (
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto']
            )[];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    /** Create Search Page */
    post: {
      /**
       * @description 1. Label: Is Required, Should be the length between 1 and 60
       * 2. Slug: Is Required, Should be the length between 1 and 60
       */
      requestBody?: {
        content: {
          'application/json-patch+json':
            | components['schemas']['CreateSearchPageCommand']
            | components['schemas']['UpdateSearchPageCommand'];
          'application/json':
            | components['schemas']['CreateSearchPageCommand']
            | components['schemas']['UpdateSearchPageCommand'];
          'text/json':
            | components['schemas']['CreateSearchPageCommand']
            | components['schemas']['UpdateSearchPageCommand'];
          'application/*+json':
            | components['schemas']['CreateSearchPageCommand']
            | components['schemas']['UpdateSearchPageCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json':
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/search-page/{id}': {
    /** Update Search Page */
    put: {
      parameters: {
        path: {
          id: string;
        };
      };
      /**
       * @description 1. Label: Is Required, Should be the length between 1 and 60
       * 2. Slug: Is Required, Should be the length between 1 and 60
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateSearchPageCommand'];
          'application/json': components['schemas']['UpdateSearchPageCommand'];
          'text/json': components['schemas']['UpdateSearchPageCommand'];
          'application/*+json': components['schemas']['UpdateSearchPageCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json':
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    delete: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/search-page/{id}/publish': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/search-page/{id}/unpublish': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/announcement-page': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': (
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto']
            )[];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    /** Create Announcement Page */
    post: {
      /**
       * @description 1. Label: Is Required, Should be the length between 1 and 60
       * 2. Slug: Is Required, Should be the length between 1 and 60
       */
      requestBody?: {
        content: {
          'application/json-patch+json':
            | components['schemas']['CreateAnnouncementPageCommand']
            | components['schemas']['UpdateAnnouncementPageCommand'];
          'application/json':
            | components['schemas']['CreateAnnouncementPageCommand']
            | components['schemas']['UpdateAnnouncementPageCommand'];
          'text/json':
            | components['schemas']['CreateAnnouncementPageCommand']
            | components['schemas']['UpdateAnnouncementPageCommand'];
          'application/*+json':
            | components['schemas']['CreateAnnouncementPageCommand']
            | components['schemas']['UpdateAnnouncementPageCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json':
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/announcement-page/{id}': {
    /** Update Announcement Page */
    put: {
      parameters: {
        path: {
          id: string;
        };
      };
      /**
       * @description 1. Label: Is Required, Should be the length between 1 and 60
       * 2. Slug: Is Required, Should be the length between 1 and 60
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateAnnouncementPageCommand'];
          'application/json': components['schemas']['UpdateAnnouncementPageCommand'];
          'text/json': components['schemas']['UpdateAnnouncementPageCommand'];
          'application/*+json': components['schemas']['UpdateAnnouncementPageCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json':
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    delete: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/announcement-page/{id}/publish': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/announcement-page/{id}/unpublish': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/simple-page': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': (
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto']
            )[];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    /** Create Simple Page */
    post: {
      /**
       * @description 1. Label: Is Required, Should be the length between 1 and 60
       * 2. Slug: Is Required, Should be the length between 1 and 60
       */
      requestBody?: {
        content: {
          'application/json-patch+json':
            | components['schemas']['CreateSimplePageCommand']
            | components['schemas']['UpdateSimplePageCommand'];
          'application/json':
            | components['schemas']['CreateSimplePageCommand']
            | components['schemas']['UpdateSimplePageCommand'];
          'text/json':
            | components['schemas']['CreateSimplePageCommand']
            | components['schemas']['UpdateSimplePageCommand'];
          'application/*+json':
            | components['schemas']['CreateSimplePageCommand']
            | components['schemas']['UpdateSimplePageCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json':
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/simple-page/{id}': {
    /** Update Simple Page */
    put: {
      parameters: {
        path: {
          id: string;
        };
      };
      /**
       * @description 1. Label: Is Required, Should be the length between 1 and 60
       * 2. Slug: Is Required, Should be the length between 1 and 60
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateSimplePageCommand'];
          'application/json': components['schemas']['UpdateSimplePageCommand'];
          'text/json': components['schemas']['UpdateSimplePageCommand'];
          'application/*+json': components['schemas']['UpdateSimplePageCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json':
              | components['schemas']['AnnouncementPageDto']
              | components['schemas']['FooterComponentDto']
              | components['schemas']['HomePageDto']
              | components['schemas']['SearchPageDto']
              | components['schemas']['SimplePageDto'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
    delete: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description No Content */
        204: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/simple-page/{id}/publish': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/page/simple-page/{id}/unpublish': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/woo-categories': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetWooCategoriesResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/woo-index-files': {
    /** @deprecated */
    get: {
      parameters: {
        query?: {
          DossierCategoryId?: string;
          Count?: number;
          Start?: number;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['CognitiveSearchResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/sitemap': {
    get: {
      parameters: {
        query?: {
          DossierCategoryId?: string;
          Count?: number;
          Start?: number;
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/xml':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/search': {
    get: {
      parameters: {
        query?: {
          Query?: string;
          Count?: number;
          Start?: number;
          Type?: components['schemas']['SearchType'][];
          PageSlug?: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['CognitiveSearchResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/settings/security': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetSecuritySettingsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/page/list': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetPublishedPageListResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/page/{slug}': {
    get: {
      parameters: {
        path: {
          slug: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetPublishedPageBySlugResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/settings/sitemap': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetSitemapSettingsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/settings/web-stats': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetWebStatsSettingsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/site-parameters': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetSiteParametersSettingsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/homepage': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['PublicHomePageResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/component/footer': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['PublicFooterComponentResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/dossier/{id}': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetLatestPublicDossierResponse'];
          };
        };
        /** @description Not Found */
        404: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/dossier/{id}/search-pages': {
    get: {
      parameters: {
        path: {
          id: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['SearchPageListItemDto'][];
          };
        };
        /** @description Not Found */
        404: {
          content: {
            'application/json': components['schemas']['NotFound'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/node/{nodeId}/children': {
    get: {
      parameters: {
        path: {
          nodeId: string;
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetPublicDossierFileStructureResponse'];
          };
        };
        /** @description Not Found */
        404: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/download/{nodeId}/{filePath}': {
    get: {
      parameters: {
        query?: {
          IpAddress?: components['schemas']['IPAddress'] | components['schemas']['ReadOnlyIPAddress'];
        };
        path: {
          nodeId: string;
          filePath: string;
        };
      };
      /** @description 1. DownloadNodeId: Should be a Guid */
      requestBody?: {
        content: {
          'multipart/form-data': {
            'Response.HttpContext.Request.Form'?: components['schemas']['StringStringValuesKeyValuePair'][];
          };
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/octet-stream':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/download/{dossierId}': {
    post: {
      parameters: {
        path: {
          dossierId: string;
        };
      };
      /** @description 1. NodeIds: Is Required */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['PublicNodesDownloadQuery'];
          'application/json': components['schemas']['PublicNodesDownloadQuery'];
          'text/json': components['schemas']['PublicNodesDownloadQuery'];
          'application/*+json': components['schemas']['PublicNodesDownloadQuery'];
        };
      };
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['PublicNodesDownloadResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/download-zip/{zipId}': {
    get: {
      parameters: {
        query?: {
          ZipId?: string;
          IpAddress?: components['schemas']['IPAddress'] | components['schemas']['ReadOnlyIPAddress'];
        };
        path: {
          zipId: string;
        };
      };
      /** @description 1. ZipId: Is Required */
      requestBody?: {
        content: {
          'multipart/form-data': {
            'Response.HttpContext.Request.Form'?: components['schemas']['StringStringValuesKeyValuePair'][];
          };
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/octet-stream':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/assets': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetSiteAssetsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/asset/fonts/{fontPath}': {
    get: {
      parameters: {
        path: {
          fontPath: string;
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/octet-stream':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/asset/images/{imagePath}': {
    get: {
      parameters: {
        path: {
          imagePath: string;
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/octet-stream':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/dossier/{imagePath}': {
    get: {
      parameters: {
        path: {
          imagePath: string;
        };
      };
      responses: {
        /** @description Server Error */
        500: {
          content: {
            'application/octet-stream':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/public/navigation-structure': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['PublicNavigationSettingsQueryResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/settings/security': {
    /** Update Security Settings */
    put: {
      /**
       * @description 1. Contact: Is Required, Iprox.Open.Core.Attributes.Api.ValidateUrlAttribute
       * 2. OpenPgpKey: Iprox.Open.Core.Attributes.Api.ValidateUrlAttribute
       * 3. Acknowledgements: Iprox.Open.Core.Attributes.Api.ValidateUrlAttribute
       * 4. Canonical: Iprox.Open.Core.Attributes.Api.ValidateUrlAttribute
       * 5. Policy: Iprox.Open.Core.Attributes.Api.ValidateUrlAttribute
       * 6. Hiring: Iprox.Open.Core.Attributes.Api.ValidateUrlAttribute
       * 7. Csaf: Iprox.Open.Core.Attributes.Api.ValidateUrlAttribute
       */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateSecuritySettingsCommand'];
          'application/json': components['schemas']['UpdateSecuritySettingsCommand'];
          'text/json': components['schemas']['UpdateSecuritySettingsCommand'];
          'application/*+json': components['schemas']['UpdateSecuritySettingsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/settings/sitemap': {
    /** Update Sitemap Settings */
    put: {
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateSitemapSettingsCommand'];
          'application/json': components['schemas']['UpdateSitemapSettingsCommand'];
          'text/json': components['schemas']['UpdateSitemapSettingsCommand'];
          'application/*+json': components['schemas']['UpdateSitemapSettingsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/settings/web-stats': {
    /** Update Web Stats Settings */
    put: {
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateWebStatsSettingsCommand'];
          'application/json': components['schemas']['UpdateWebStatsSettingsCommand'];
          'text/json': components['schemas']['UpdateWebStatsSettingsCommand'];
          'application/*+json': components['schemas']['UpdateWebStatsSettingsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/settings/site-parameters': {
    /** Update Site Parameters */
    put: {
      /** @description 1. TenantName: Is Required */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateSiteParametersSettingsCommand'];
          'application/json': components['schemas']['UpdateSiteParametersSettingsCommand'];
          'text/json': components['schemas']['UpdateSiteParametersSettingsCommand'];
          'application/*+json': components['schemas']['UpdateSiteParametersSettingsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/settings/page/home': {
    /** Update Home Page Content */
    put: {
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateHomepageContentSettingsCommand'];
          'application/json': components['schemas']['UpdateHomepageContentSettingsCommand'];
          'text/json': components['schemas']['UpdateHomepageContentSettingsCommand'];
          'application/*+json': components['schemas']['UpdateHomepageContentSettingsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/settings/navigation': {
    /** Update Navigation */
    put: {
      /** @description 1. Navigation: Is Required */
      requestBody?: {
        content: {
          'application/json-patch+json': components['schemas']['UpdateNavigationSettingsCommand'];
          'application/json': components['schemas']['UpdateNavigationSettingsCommand'];
          'text/json': components['schemas']['UpdateNavigationSettingsCommand'];
          'application/*+json': components['schemas']['UpdateNavigationSettingsCommand'];
        };
      };
      responses: {
        /** @description Success */
        200: never;
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/user/me': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetLoggedInUserResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
  '/api/v1/user/roles': {
    get: {
      responses: {
        /** @description Success */
        200: {
          content: {
            'application/json': components['schemas']['GetUserRolesWithPermissionsResponse'];
          };
        };
        /** @description Server Error */
        500: {
          content: {
            'application/json':
              | components['schemas']['ProblemDetails']
              | components['schemas']['HttpValidationProblemDetails'];
          };
        };
      };
    };
  };
}

export type webhooks = Record<string, never>;

export interface components {
  schemas: {
    ASCIIEncoding: WithRequired<
      {
        isSingleByte: boolean;
      } & components['schemas']['Encoding'],
      'isSingleByte'
    >;
    ASCIIEncodingSealed: components['schemas']['ASCIIEncoding'];
    AcceptedAtActionResult: WithRequired<
      {
        urlHelper: components['schemas']['IUrlHelper'] | null;
        actionName: string | null;
        controllerName: string | null;
        routeValues: {
          [key: string]: unknown;
        } | null;
      } & components['schemas']['ObjectResult'],
      'actionName' | 'controllerName' | 'routeValues' | 'urlHelper'
    >;
    AcceptedAtRouteResult: WithRequired<
      {
        urlHelper: components['schemas']['IUrlHelper'] | null;
        routeName: string | null;
        routeValues: {
          [key: string]: unknown;
        } | null;
      } & components['schemas']['ObjectResult'],
      'routeName' | 'routeValues' | 'urlHelper'
    >;
    AcceptedResult: WithRequired<
      {
        location: string | null;
      } & components['schemas']['ObjectResult'],
      'location'
    >;
    ActionContext: {
      actionDescriptor: components['schemas']['ActionDescriptor'];
      httpContext: components['schemas']['HttpContext'];
      modelState: {
        [key: string]: components['schemas']['ModelStateNode'] | undefined;
      };
      routeData: components['schemas']['RouteData'];
    };
    ActionContextBooleanFunc: {
      target: Record<string, unknown> | null;
      method:
        | components['schemas']['RuntimeMethodInfo']
        | components['schemas']['DynamicMethod']
        | components['schemas']['RuntimeMethodBuilder']
        | components['schemas']['SymbolMethod']
        | components['schemas']['MethodBuilder']
        | components['schemas']['MethodBuilderInstantiation']
        | components['schemas']['MethodOnTypeBuilderInstantiation'];
    };
    ActionDescriptor: {
      id: string;
      routeValues: {
        [key: string]: string | undefined;
      };
      attributeRouteInfo: components['schemas']['AttributeRouteInfo'] | null;
      actionConstraints: components['schemas']['IActionConstraintMetadata'][] | null;
      endpointMetadata: unknown[];
      parameters: components['schemas']['ParameterDescriptor'][];
      boundProperties: components['schemas']['ParameterDescriptor'][];
      filterDescriptors: components['schemas']['FilterDescriptor'][];
      displayName: string | null;
      properties: {
        [key: string]: unknown;
      };
    };
    ActionExecutedContext: WithRequired<
      {
        canceled: boolean;
        controller: unknown;
        exception: Record<string, unknown> | null;
        exceptionDispatchInfo: components['schemas']['ExceptionDispatchInfo'] | null;
        exceptionHandled: boolean;
        result: components['schemas']['IActionResult'] | null;
      } & components['schemas']['FilterContext'],
      'canceled' | 'controller' | 'exception' | 'exceptionDispatchInfo' | 'exceptionHandled' | 'result'
    >;
    ActionExecutingContext: WithRequired<
      {
        result: components['schemas']['IActionResult'] | null;
        actionArguments: {
          [key: string]: unknown;
        };
        controller: unknown;
      } & components['schemas']['FilterContext'],
      'actionArguments' | 'controller' | 'result'
    >;
    ActionResult: Record<string, never>;
    AddDossierCategoryDynamicFieldResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    AddDynamicFieldValidationRuleCommand: {
      /** Format: uuid */
      dynamicFieldId: string;
      validationRuleType: components['schemas']['DynamicFieldValidationRuleType'];
      fieldRuleValue: components['schemas']['ValidationRuleEventObject'];
    };
    AddDynamicFieldValidationRuleResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    /** @enum {string} */
    AddressFamily:
      | 'Unspecified'
      | 'Unix'
      | 'InterNetwork'
      | 'ImpLink'
      | 'Pup'
      | 'Chaos'
      | 'NS'
      | 'Ipx'
      | 'Iso'
      | 'Osi'
      | 'Ecma'
      | 'DataKit'
      | 'Ccitt'
      | 'Sna'
      | 'DecNet'
      | 'DataLink'
      | 'Lat'
      | 'HyperChannel'
      | 'AppleTalk'
      | 'NetBios'
      | 'VoiceView'
      | 'FireFox'
      | 'Banyan'
      | 'Atm'
      | 'InterNetworkV6'
      | 'Cluster'
      | 'Ieee12844'
      | 'Irda'
      | 'NetworkDesigners'
      | 'Max'
      | 'Packet'
      | 'ControllerAreaNetwork'
      | 'Unknown';
    AnnouncementPage: WithRequired<
      {
        /** @enum {unknown} */
        pageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
        organisationName: string;
        organisationType: string;
        publicationType: string;
        breakOnZoneValidationError: boolean;
        pageZones: readonly components['schemas']['PageZone'][];
      } & components['schemas']['Page'],
      | 'breakOnZoneValidationError'
      | 'organisationName'
      | 'organisationType'
      | 'pageType'
      | 'pageZones'
      | 'publicationType'
    >;
    AnnouncementPageDto: WithRequired<
      {
        organisationName: string;
        organisationType: string;
        publicationType: string;
      } & components['schemas']['PageDto'],
      'organisationName' | 'organisationType' | 'publicationType'
    >;
    AntiforgeryValidationFailedResult: components['schemas']['BadRequestResult'];
    /** @enum {string} */
    ApartmentState: 'STA' | 'MTA' | 'Unknown';
    AppDomain: WithRequired<
      {
        baseDirectory: string;
        relativeSearchPath: string | null;
        setupInformation: components['schemas']['AppDomainSetup'];
        /** @deprecated */
        permissionSet: readonly unknown[];
        dynamicDirectory: string | null;
        friendlyName: string;
        /** Format: int32 */
        id: number;
        isFullyTrusted: boolean;
        isHomogenous: boolean;
        /** Format: int64 */
        monitoringSurvivedMemorySize: number;
        /** Format: int64 */
        monitoringTotalAllocatedMemorySize: number;
        shadowCopyFiles: boolean;
        /** Format: date-span */
        monitoringTotalProcessorTime: string;
      } & components['schemas']['MarshalByRefObject'],
      | 'baseDirectory'
      | 'dynamicDirectory'
      | 'friendlyName'
      | 'id'
      | 'isFullyTrusted'
      | 'isHomogenous'
      | 'monitoringSurvivedMemorySize'
      | 'monitoringTotalAllocatedMemorySize'
      | 'monitoringTotalProcessorTime'
      | 'permissionSet'
      | 'relativeSearchPath'
      | 'setupInformation'
      | 'shadowCopyFiles'
    >;
    AppDomainSetup: {
      applicationBase: string | null;
      targetFrameworkName: string | null;
    };
    ApplicationSetting: WithRequired<
      {
        settingName: string;
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'] | null;
        settings: readonly components['schemas']['ApplicationSettingDetail'][];
      } & components['schemas']['BaseEntity'],
      'created' | 'modified' | 'settingName' | 'settings'
    >;
    ApplicationSettingDetail: WithRequired<
      {
        key: string;
        value: string | null;
        applicationSetting: components['schemas']['ApplicationSetting'];
        /** Format: uuid */
        applicationSettingId: string;
      } & components['schemas']['BaseEntity'],
      'applicationSetting' | 'applicationSettingId' | 'key' | 'value'
    >;
    AsnEncodedData: {
      oid: components['schemas']['Oid'] | null;
      /** Format: byte */
      rawData: string;
    };
    Assembly: {
      definedTypes: readonly string[];
      exportedTypes: readonly string[];
      /** @deprecated */
      codeBase: string | null;
      entryPoint:
        | components['schemas']['RuntimeMethodInfo']
        | components['schemas']['DynamicMethod']
        | components['schemas']['RuntimeMethodBuilder']
        | components['schemas']['SymbolMethod']
        | components['schemas']['MethodBuilder']
        | components['schemas']['MethodBuilderInstantiation']
        | components['schemas']['MethodOnTypeBuilderInstantiation']
        | null;
      fullName: string | null;
      imageRuntimeVersion: string;
      isDynamic: boolean;
      location: string;
      reflectionOnly: boolean;
      isCollectible: boolean;
      isFullyTrusted: boolean;
      customAttributes: readonly (
        | components['schemas']['CustomAttributeData']
        | components['schemas']['RuntimeCustomAttributeData']
      )[];
      /** @deprecated */
      escapedCodeBase: string;
      manifestModule:
        | components['schemas']['RuntimeModule']
        | components['schemas']['RuntimeModuleBuilder']
        | components['schemas']['ModuleBuilder'];
      modules: readonly (
        | components['schemas']['RuntimeModule']
        | components['schemas']['RuntimeModuleBuilder']
        | components['schemas']['ModuleBuilder']
      )[];
      /** @deprecated */
      globalAssemblyCache: boolean;
      /** Format: int64 */
      hostContext: number;
      /** @enum {unknown} */
      securityRuleSet: 'None' | 'Level1' | 'Level2';
    };
    AssemblyBuilder: WithRequired<
      {
        /** @deprecated */
        codeBase: string | null;
        location: string;
        entryPoint:
          | components['schemas']['RuntimeMethodInfo']
          | components['schemas']['DynamicMethod']
          | components['schemas']['RuntimeMethodBuilder']
          | components['schemas']['SymbolMethod']
          | components['schemas']['MethodBuilder']
          | components['schemas']['MethodBuilderInstantiation']
          | components['schemas']['MethodOnTypeBuilderInstantiation']
          | null;
        isDynamic: boolean;
      } & components['schemas']['Assembly'],
      'codeBase' | 'entryPoint' | 'isDynamic' | 'location'
    >;
    AssemblyNameProxy: components['schemas']['MarshalByRefObject'];
    Asset: WithRequired<
      {
        /** @enum {unknown} */
        siteAssetType: 'Image' | 'Font';
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'] | null;
      } & components['schemas']['BaseEntity'],
      'created' | 'modified' | 'siteAssetType'
    >;
    AsymmetricAlgorithm: {
      /** Format: int32 */
      keySize: number;
      legalKeySizes: readonly components['schemas']['KeySizes'][];
      signatureAlgorithm: string | null;
      keyExchangeAlgorithm: string | null;
    };
    AsyncWindowsFileStreamStrategy: components['schemas']['OSFileStreamStrategy'];
    AttributeRouteInfo: {
      template: string | null;
      /** Format: int32 */
      order: number;
      name: string | null;
      suppressLinkGeneration: boolean;
      suppressPathMatching: boolean;
    };
    AuthenticationProperties: {
      items: {
        [key: string]: string | undefined;
      };
      parameters: {
        [key: string]: unknown;
      };
      isPersistent: boolean;
      redirectUri: string | null;
      /** Format: date-time */
      issuedUtc: string | null;
      /** Format: date-time */
      expiresUtc: string | null;
      allowRefresh: boolean | null;
    };
    AuthorizationFilterContext: WithRequired<
      {
        result: components['schemas']['IActionResult'] | null;
      } & components['schemas']['FilterContext'],
      'result'
    >;
    AuthorizedNodeDownloadRecord: WithRequired<
      {
        /** Format: uuid */
        userId: string;
        /** Format: uuid */
        nodeId: string;
        /** @enum {unknown} */
        downloadRecordType: 'AuthorizedNodeDownload' | 'PublicNodeDownload' | 'PublicZipDownload';
      } & components['schemas']['DownloadRecord'],
      'downloadRecordType' | 'nodeId' | 'userId'
    >;
    AutoResetEvent: components['schemas']['EventWaitHandle'];
    BadRequestObjectResult: components['schemas']['ObjectResult'];
    BadRequestResult: components['schemas']['StatusCodeResult'];
    BaseDossierView: WithRequired<
      {
        /** Format: uuid */
        id: string;
        /** Format: uuid */
        dossierEntityId: string;
        /** Format: uuid */
        dossierId: string;
        /** Format: int32 */
        dossierVersion: number;
        dossierTitle: string;
        dossierCategory: string;
        dossierCategoryAlias: string;
        /** Format: uuid */
        dossierCategoryId: string;
        dossierSummary: string;
        /** @enum {unknown} */
        status: 'Draft' | 'Published' | 'Unpublished' | 'Deleted';
        /** Format: date-time */
        publishedDateTime: string;
        /** Format: date-time */
        publishFromDate: string;
        /** Format: date-time */
        publishToDate: string;
        /** Format: date-time */
        createdDateTime: string;
        /** Format: date-time */
        modifiedDateTime: string;
        dynamicFields: {
          [key: string]: unknown;
        };
      } & components['schemas']['BaseView'],
      | 'createdDateTime'
      | 'dossierCategory'
      | 'dossierCategoryAlias'
      | 'dossierCategoryId'
      | 'dossierEntityId'
      | 'dossierId'
      | 'dossierSummary'
      | 'dossierTitle'
      | 'dossierVersion'
      | 'dynamicFields'
      | 'id'
      | 'modifiedDateTime'
      | 'publishedDateTime'
      | 'publishFromDate'
      | 'publishToDate'
      | 'status'
    >;
    BaseEntity: {
      /** Format: uuid */
      id: string;
    };
    BaseFileView: WithRequired<
      {
        /** Format: uuid */
        id: string;
        /** Format: uuid */
        nodeId: string;
        /** Format: uuid */
        dossierId: string;
        /** Format: int32 */
        dossierVersion: number;
        dossierTitle: string;
        dossierCategory: string;
        dossierCategoryAlias: string;
        /** Format: uuid */
        dossierCategoryId: string;
        nodeName: string;
        nodePath: string;
        isDeleted: boolean;
        /** @enum {unknown} */
        status: 'Draft' | 'Published' | 'Unpublished' | 'Deleted';
        /** Format: date-time */
        publishedDateTime: string | null;
        /** Format: date-time */
        publishFromDate: string;
        /** Format: date-time */
        publishToDate: string;
        /** Format: date-time */
        createdDateTime: string;
        /** Format: date-time */
        modifiedDateTime: string;
      } & components['schemas']['BaseView'],
      | 'createdDateTime'
      | 'dossierCategory'
      | 'dossierCategoryAlias'
      | 'dossierCategoryId'
      | 'dossierId'
      | 'dossierTitle'
      | 'dossierVersion'
      | 'id'
      | 'isDeleted'
      | 'modifiedDateTime'
      | 'nodeId'
      | 'nodeName'
      | 'nodePath'
      | 'publishedDateTime'
      | 'publishFromDate'
      | 'publishToDate'
      | 'status'
    >;
    BasePageDto: WithRequired<
      {
        /** Format: date-time */
        createdDate: string;
        /** Format: date-time */
        modifiedDate: string;
      } & components['schemas']['PageInfoDto'],
      'createdDate' | 'modifiedDate'
    >;
    BasePageListItemDto: components['schemas']['PageInfoDto'];
    BaseResponse: Record<string, never>;
    BaseView: Record<string, never>;
    BindingInfo: {
      bindingSource: components['schemas']['BindingSource'] | components['schemas']['CompositeBindingSource'] | null;
      binderModelName: string | null;
      binderType: string | null;
      propertyFilterProvider: components['schemas']['IPropertyFilterProvider'] | null;
      requestPredicate: components['schemas']['ActionContextBooleanFunc'] | null;
      /** @enum {unknown} */
      emptyBodyBehavior: 'Default' | 'Allow' | 'Disallow';
      serviceKey: Record<string, unknown> | null;
    };
    BindingSource: {
      displayName: string;
      id: string;
      isGreedy: boolean;
      isFromRequest: boolean;
    };
    BlockContent: Record<string, never>;
    BlockContentDto: Record<string, never>;
    BlockContentUpdateModel: Record<string, never>;
    /** @enum {string} */
    BlockType: 'RichText';
    BufferedFileStreamStrategy: WithRequired<
      {
        canRead: boolean;
        canWrite: boolean;
        canSeek: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['FileStreamStrategy'],
      'canRead' | 'canSeek' | 'canWrite' | 'length' | 'position'
    >;
    BufferedStream: WithRequired<
      {
        underlyingStream:
          | components['schemas']['TranscodingStream']
          | components['schemas']['ManifestResourceStream']
          | components['schemas']['BufferedStream']
          | components['schemas']['FileStream']
          | components['schemas']['MemoryStream']
          | components['schemas']['PinnedBufferMemoryStream']
          | components['schemas']['NullStream']
          | components['schemas']['SyncStream']
          | components['schemas']['UnmanagedMemoryStream']
          | components['schemas']['UnmanagedMemoryStreamWrapper']
          | components['schemas']['BufferedFileStreamStrategy']
          | components['schemas']['DerivedFileStreamStrategy']
          | components['schemas']['FileStreamStrategy']
          | components['schemas']['OSFileStreamStrategy']
          | components['schemas']['AsyncWindowsFileStreamStrategy']
          | components['schemas']['SyncWindowsFileStreamStrategy'];
        /** Format: int32 */
        bufferSize: number;
        canRead: boolean;
        canWrite: boolean;
        canSeek: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['Stream'],
      'bufferSize' | 'canRead' | 'canSeek' | 'canWrite' | 'length' | 'position' | 'underlyingStream'
    >;
    'Byte*': Record<string, never>;
    ByteReadOnlyMemory: {
      /** Format: int32 */
      length: number;
      isEmpty: boolean;
    };
    CallbackResetEvent: components['schemas']['EventWaitHandle'];
    /** @enum {string} */
    CallingConventions: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
    CancellationToken: {
      isCancellationRequested: boolean;
      canBeCanceled: boolean;
      waitHandle:
        | components['schemas']['AutoResetEvent']
        | components['schemas']['EventWaitHandle']
        | components['schemas']['ManualResetEvent']
        | components['schemas']['Mutex']
        | components['schemas']['Semaphore']
        | components['schemas']['CallbackResetEvent'];
    };
    ChallengeResult: WithRequired<
      {
        authenticationSchemes: string[];
        properties: components['schemas']['AuthenticationProperties'] | null;
      } & components['schemas']['ActionResult'],
      'authenticationSchemes' | 'properties'
    >;
    Claim: {
      issuer: string;
      originalIssuer: string;
      properties: {
        [key: string]: string | undefined;
      };
      subject: components['schemas']['ClaimsIdentity'] | components['schemas']['GenericIdentity'] | null;
      type: string;
      value: string;
      valueType: string;
    };
    ClaimsIdentity: {
      authenticationType: string | null;
      isAuthenticated: boolean;
      actor: components['schemas']['ClaimsIdentity'] | components['schemas']['GenericIdentity'] | null;
      bootstrapContext: Record<string, unknown> | null;
      claims: components['schemas']['Claim'][];
      label: string | null;
      name: string | null;
      nameClaimType: string;
      roleClaimType: string;
    };
    ClaimsPrincipal: {
      claims: readonly components['schemas']['Claim'][];
      identities: (components['schemas']['ClaimsIdentity'] | components['schemas']['GenericIdentity'])[];
      identity: components['schemas']['IIdentity'] | null;
    };
    CngAlgorithm: {
      algorithm: string;
    };
    CngAlgorithmGroup: {
      algorithmGroup: string;
    };
    /** @enum {string} */
    CngExportPolicies: 'None' | 'AllowExport' | 'AllowPlaintextExport' | 'AllowArchiving' | 'AllowPlaintextArchiving';
    CngKey: {
      algorithm: components['schemas']['CngAlgorithm'];
      algorithmGroup: components['schemas']['CngAlgorithmGroup'] | null;
      /** @enum {unknown} */
      exportPolicy: 'None' | 'AllowExport' | 'AllowPlaintextExport' | 'AllowArchiving' | 'AllowPlaintextArchiving';
      handle: components['schemas']['SafeNCryptKeyHandle'];
      isEphemeral: boolean;
      isMachineKey: boolean;
      keyName: string | null;
      /** Format: int32 */
      keySize: number;
      /** @enum {unknown} */
      keyUsage: 'None' | 'Decryption' | 'Signing' | 'KeyAgreement' | 'AllUsages';
      parentWindowHandle: unknown;
      provider: components['schemas']['CngProvider'] | null;
      providerHandle: components['schemas']['SafeNCryptProviderHandle'];
      uiPolicy: components['schemas']['CngUIPolicy'];
      uniqueName: string | null;
    };
    CngKeyBlobFormat: {
      format: string;
    };
    /** @enum {string} */
    CngKeyUsages: 'None' | 'Decryption' | 'Signing' | 'KeyAgreement' | 'AllUsages';
    CngProvider: {
      provider: string;
    };
    CngUIPolicy: {
      /** @enum {unknown} */
      protectionLevel: 'None' | 'ProtectKey' | 'ForceHighProtection';
      friendlyName: string | null;
      description: string | null;
      useContext: string | null;
      creationTitle: string | null;
    };
    /** @enum {string} */
    CngUIProtectionLevels: 'None' | 'ProtectKey' | 'ForceHighProtection';
    CognitiveSearchResponse: WithRequired<
      {
        /** Format: int32 */
        count: number;
        /** Format: int32 */
        start: number;
        /** Format: int32 */
        itemCount: number;
        /** Format: int64 */
        totalCount: number;
        items: (
          | components['schemas']['PublicCognitiveBaseSearchResultDto']
          | components['schemas']['PublicCognitiveDossierSearchResultDto']
          | components['schemas']['PublicCognitiveFileSearchResultDto']
        )[];
      } & components['schemas']['BaseResponse'],
      'count' | 'itemCount' | 'items' | 'start' | 'totalCount'
    >;
    CompositeBindingSource: WithRequired<
      {
        bindingSources: readonly (
          | components['schemas']['BindingSource']
          | components['schemas']['CompositeBindingSource']
        )[];
      } & components['schemas']['BindingSource'],
      'bindingSources'
    >;
    ConflictObjectResult: components['schemas']['ObjectResult'];
    ConflictResult: components['schemas']['StatusCodeResult'];
    ConnectionInfo: {
      id: string;
      remoteIpAddress: components['schemas']['IPAddress'] | components['schemas']['ReadOnlyIPAddress'] | null;
      /** Format: int32 */
      remotePort: number;
      localIpAddress: components['schemas']['IPAddress'] | components['schemas']['ReadOnlyIPAddress'] | null;
      /** Format: int32 */
      localPort: number;
      clientCertificate: components['schemas']['X509Certificate2'] | null;
    };
    ConstructorBuilder: WithRequired<
      {
        initLocals: boolean;
      } & components['schemas']['ConstructorInfo'],
      'initLocals'
    >;
    ConstructorInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
      } & components['schemas']['MethodBase'],
      'memberType'
    >;
    ConstructorOnTypeBuilderInstantiation: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        isGenericMethodDefinition: boolean;
        containsGenericParameters: boolean;
        isGenericMethod: boolean;
      } & components['schemas']['ConstructorInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'containsGenericParameters'
      | 'declaringType'
      | 'isGenericMethod'
      | 'isGenericMethodDefinition'
      | 'memberType'
      | 'metadataToken'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
    >;
    /** @enum {string} */
    ContentIndexType: 'File' | 'Dossier';
    ContentResult: WithRequired<
      {
        content: string | null;
        contentType: string | null;
        /** Format: int32 */
        statusCode: number | null;
      } & components['schemas']['ActionResult'],
      'content' | 'contentType' | 'statusCode'
    >;
    ContextBoundObject: components['schemas']['MarshalByRefObject'];
    CreateAnnouncementPageCommand: {
      label: string;
      slug: string;
    };
    CreateDossierCategoryCommand: {
      alias: string;
      label: string;
      requirePublishDates: boolean;
    };
    CreateDossierCategoryResponse: WithRequired<
      {
        dossierCategory: components['schemas']['DossierCategoryDto'];
      } & components['schemas']['BaseResponse'],
      'dossierCategory'
    >;
    CreateDossierCommand: {
      title: string;
      /** Format: uuid */
      categoryId: string;
    };
    CreateDossierResponse: WithRequired<
      {
        dossier: components['schemas']['DossierDto'];
      } & components['schemas']['BaseResponse'],
      'dossier'
    >;
    CreateDynamicFieldCommand: {
      alias: string;
      label: string;
      helpText: string;
      /** @enum {unknown} */
      dynamicFieldType:
        | 'Text'
        | 'Integer'
        | 'Decimal'
        | 'Date'
        | 'Select'
        | 'RadioButton'
        | 'CheckBox'
        | 'TextArea'
        | 'StringList'
        | 'RichText'
        | 'DateTimeRange';
      defaultValue: unknown;
      options: {
        [key: string]: string | undefined;
      };
    };
    CreateDynamicFieldResponse: WithRequired<
      {
        dynamicField: components['schemas']['DynamicFieldDto'];
      } & components['schemas']['BaseResponse'],
      'dynamicField'
    >;
    CreateFolderCommand: {
      name: string;
    };
    CreateFolderResponse: WithRequired<
      {
        /** Format: uuid */
        folderId: string;
      } & components['schemas']['BaseResponse'],
      'folderId'
    >;
    CreateFontAssetCommand: {
      /** @enum {unknown} */
      fontCategory: 'Heading' | 'Text' | 'Footer' | 'Undefined';
      fontFamily: string;
      /** @enum {unknown} */
      fontWeight: 'W100' | 'W200' | 'W300' | 'W400' | 'W500' | 'W600' | 'W700' | 'W800' | 'W900' | 'Undefined';
      /** @enum {unknown} */
      fontStyle: 'Normal' | 'Italic' | 'Oblique' | 'Initial' | 'Inherit' | 'Undefined';
    };
    CreateFontAssetResponse: {
      fontAsset: components['schemas']['FontAssetDto'];
    };
    CreatePageResponse: WithRequired<
      {
        page:
          | components['schemas']['AnnouncementPageDto']
          | components['schemas']['FooterComponentDto']
          | components['schemas']['HomePageDto']
          | components['schemas']['SearchPageDto']
          | components['schemas']['SimplePageDto'];
      } & components['schemas']['BaseResponse'],
      'page'
    >;
    CreateSearchPageCommand: {
      label: string;
      slug: string;
    };
    CreateSimplePageCommand: {
      label: string;
      slug: string;
    };
    CreatedAtActionResult: WithRequired<
      {
        urlHelper: components['schemas']['IUrlHelper'] | null;
        actionName: string | null;
        controllerName: string | null;
        routeValues: {
          [key: string]: unknown;
        } | null;
      } & components['schemas']['ObjectResult'],
      'actionName' | 'controllerName' | 'routeValues' | 'urlHelper'
    >;
    CreatedAtRouteResult: WithRequired<
      {
        urlHelper: components['schemas']['IUrlHelper'] | null;
        routeName: string | null;
        routeValues: {
          [key: string]: unknown;
        } | null;
      } & components['schemas']['ObjectResult'],
      'routeName' | 'routeValues' | 'urlHelper'
    >;
    CreatedResult: WithRequired<
      {
        location: string | null;
      } & components['schemas']['ObjectResult'],
      'location'
    >;
    CriticalFinalizerObject: Record<string, never>;
    CriticalHandle: WithRequired<
      {
        isClosed: boolean;
        isInvalid: boolean;
      } & components['schemas']['CriticalFinalizerObject'],
      'isClosed' | 'isInvalid'
    >;
    CriticalHandleMinusOneIsInvalid: WithRequired<
      {
        isInvalid: boolean;
      } & components['schemas']['CriticalHandle'],
      'isInvalid'
    >;
    CriticalHandleZeroOrMinusOneIsInvalid: WithRequired<
      {
        isInvalid: boolean;
      } & components['schemas']['CriticalHandle'],
      'isInvalid'
    >;
    CspKeyContainerInfo: {
      accessible: boolean;
      exportable: boolean;
      hardwareDevice: boolean;
      keyContainerName: string | null;
      /** @enum {unknown} */
      keyNumber: 'Exchange' | 'Signature';
      machineKeyStore: boolean;
      protected: boolean;
      providerName: string | null;
      /** Format: int32 */
      providerType: number;
      randomlyGenerated: boolean;
      removable: boolean;
      uniqueKeyContainerName: string;
    };
    CustomAttributeData: {
      attributeType: string;
      constructor:
        | components['schemas']['RuntimeConstructorInfo']
        | components['schemas']['RuntimeConstructorBuilder']
        | components['schemas']['ConstructorBuilder']
        | components['schemas']['ConstructorOnTypeBuilderInstantiation'];
      constructorArguments: readonly components['schemas']['CustomAttributeTypedArgument'][];
      namedArguments: readonly components['schemas']['CustomAttributeNamedArgument'][];
    };
    CustomAttributeNamedArgument: {
      memberInfo:
        | string
        | components['schemas']['ConstructorInfo']
        | components['schemas']['FieldInfo']
        | components['schemas']['MdFieldInfo']
        | components['schemas']['MethodBase']
        | components['schemas']['RtFieldInfo']
        | components['schemas']['RuntimeConstructorInfo']
        | components['schemas']['RuntimeEventInfo']
        | components['schemas']['RuntimeFieldInfo']
        | components['schemas']['RuntimeMethodInfo']
        | components['schemas']['RuntimePropertyInfo']
        | components['schemas']['EventInfo']
        | components['schemas']['MethodInfo']
        | components['schemas']['PropertyInfo']
        | components['schemas']['DynamicMethod']
        | components['schemas']['RuntimeConstructorBuilder']
        | components['schemas']['RuntimeFieldBuilder']
        | components['schemas']['RuntimeMethodBuilder']
        | components['schemas']['RuntimePropertyBuilder']
        | components['schemas']['SymbolMethod']
        | components['schemas']['ConstructorBuilder']
        | components['schemas']['ConstructorOnTypeBuilderInstantiation']
        | components['schemas']['FieldBuilder']
        | components['schemas']['FieldOnTypeBuilderInstantiation']
        | components['schemas']['MethodBuilder']
        | components['schemas']['MethodBuilderInstantiation']
        | components['schemas']['MethodOnTypeBuilderInstantiation']
        | components['schemas']['PropertyBuilder'];
      typedValue: components['schemas']['CustomAttributeTypedArgument'];
      memberName: string;
      isField: boolean;
    };
    CustomAttributeTypedArgument: {
      argumentType: string;
      value: Record<string, unknown> | null;
    };
    DSA: components['schemas']['AsymmetricAlgorithm'];
    DSACng: WithRequired<
      {
        legalKeySizes: readonly components['schemas']['KeySizes'][];
        signatureAlgorithm: string;
        keyExchangeAlgorithm: string | null;
        key: components['schemas']['CngKey'];
      } & components['schemas']['DSA'],
      'key' | 'keyExchangeAlgorithm' | 'legalKeySizes' | 'signatureAlgorithm'
    >;
    DSACryptoServiceProvider: WithRequired<
      {
        cspKeyContainerInfo: components['schemas']['CspKeyContainerInfo'];
        /** Format: int32 */
        keySize: number;
        legalKeySizes: readonly components['schemas']['KeySizes'][];
        persistKeyInCsp: boolean;
        publicOnly: boolean;
        keyExchangeAlgorithm: string | null;
        signatureAlgorithm: string;
      } & components['schemas']['DSA'],
      | 'cspKeyContainerInfo'
      | 'keyExchangeAlgorithm'
      | 'keySize'
      | 'legalKeySizes'
      | 'persistKeyInCsp'
      | 'publicOnly'
      | 'signatureAlgorithm'
    >;
    DSAOpenSsl: components['schemas']['DSA'];
    DSAWrapper: WithRequired<
      {
        /** Format: int32 */
        keySize: number;
        legalKeySizes: readonly components['schemas']['KeySizes'][] | null;
        signatureAlgorithm: string | null;
        keyExchangeAlgorithm: string | null;
      } & components['schemas']['DSA'],
      'keyExchangeAlgorithm' | 'keySize' | 'legalKeySizes' | 'signatureAlgorithm'
    >;
    DecoderExceptionFallback: WithRequired<
      {
        /** Format: int32 */
        maxCharCount: number;
      } & components['schemas']['DecoderFallback'],
      'maxCharCount'
    >;
    DecoderFallback: {
      /** Format: int32 */
      maxCharCount: number;
    };
    DecoderReplacementFallback: WithRequired<
      {
        defaultString: string;
        /** Format: int32 */
        maxCharCount: number;
      } & components['schemas']['DecoderFallback'],
      'defaultString' | 'maxCharCount'
    >;
    DecoderUTF7Fallback: WithRequired<
      {
        /** Format: int32 */
        maxCharCount: number;
      } & components['schemas']['DecoderFallback'],
      'maxCharCount'
    >;
    DefaultPipeReader: components['schemas']['PipeReader'];
    DefaultPipeWriter: WithRequired<
      {
        canGetUnflushedBytes: boolean;
        /** Format: int64 */
        unflushedBytes: number;
      } & components['schemas']['PipeWriter'],
      'canGetUnflushedBytes' | 'unflushedBytes'
    >;
    DeleteAnnouncementPageCommand: {
      /** Format: uuid */
      id: string;
    };
    DeleteDossierCategoryCommand: {
      /** Format: uuid */
      dossierCategoryId: string;
    };
    DeleteDossierCategoryDynamicFieldResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    DeleteDossierCategoryResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    DeleteDossierCommand: {
      /** Format: uuid */
      dossierId: string;
    };
    DeleteDossierImageCommand: {
      /** Format: uuid */
      dossierId: string;
    };
    DeleteDossierResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    DeleteDynamicFieldCommand: {
      /** Format: uuid */
      dynamicFieldId: string;
    };
    DeleteDynamicFieldValidationRuleCommand: {
      /** Format: uuid */
      dynamicFieldId: string;
      validationRuleType: components['schemas']['DynamicFieldValidationRuleType'];
    };
    DeleteFileFolderNodesCommand: {
      nodeIds: string[];
    };
    DeleteFileFolderNodesResponse: WithRequired<
      {
        removedNodeIds: string[];
      } & components['schemas']['BaseResponse'],
      'removedNodeIds'
    >;
    DeleteFontAssetCommand: {
      /** Format: uuid */
      fontAssetId: string;
    };
    DeleteFontAssetResponse: {
      success: boolean;
    };
    DeleteFontsCommand: {
      /** Format: uuid */
      fontAssetId: string;
      fontIds: string[];
    };
    DeleteFontsResponse: {
      deletedNodes: string[];
      notDeletedNodes: string[];
    };
    DeleteSearchPageCommand: {
      /** Format: uuid */
      id: string;
    };
    DeleteSimplePageCommand: {
      /** Format: uuid */
      id: string;
    };
    DerivedFileStreamStrategy: WithRequired<
      {
        canRead: boolean;
        canWrite: boolean;
        canSeek: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['FileStreamStrategy'],
      'canRead' | 'canSeek' | 'canWrite' | 'length' | 'position'
    >;
    Descriptor: {
      key: string;
      value: string;
    };
    DescriptorDto: {
      key: string;
      value: string;
    };
    DirectoryInfo: WithRequired<
      {
        name: string;
        parent: components['schemas']['DirectoryInfo'] | null;
        root: components['schemas']['DirectoryInfo'];
        exists: boolean;
      } & components['schemas']['FileSystemInfo'],
      'exists' | 'name' | 'parent' | 'root'
    >;
    Dossier: WithRequired<
      {
        /** Format: uuid */
        dossierId: string;
        /** Format: int32 */
        version: number;
        title: string;
        /** Format: uuid */
        categoryId: string;
        summary: string | null;
        /** Format: uuid */
        rootFolderNodeId: string | null;
        /** Format: uuid */
        decorativeImageNodeId: string | null;
        /** @enum {unknown} */
        status: 'Draft' | 'Published' | 'Unpublished' | 'Deleted';
        isPublishedAndVisible: boolean;
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'] | null;
        publishDates: components['schemas']['PublishDateInfo'];
        published: components['schemas']['ModificationInfo'] | null;
        dossierPublished: components['schemas']['ModificationInfo'] | null;
        unpublished: components['schemas']['ModificationInfo'] | null;
        deleted: components['schemas']['ModificationInfo'] | null;
        category: components['schemas']['DossierCategory'];
        rootFolderNode: components['schemas']['DossierFileStructureView'] | null;
        decorativeImageNode: components['schemas']['ImageNode'] | null;
        dynamicFieldValues: readonly components['schemas']['DossierDynamicFieldValue'][];
        nodes: readonly (
          | components['schemas']['FileNode']
          | components['schemas']['FolderNode']
          | components['schemas']['ImageNode']
        )[];
      } & components['schemas']['BaseEntity'],
      | 'category'
      | 'categoryId'
      | 'created'
      | 'decorativeImageNode'
      | 'decorativeImageNodeId'
      | 'deleted'
      | 'dossierId'
      | 'dossierPublished'
      | 'dynamicFieldValues'
      | 'isPublishedAndVisible'
      | 'modified'
      | 'nodes'
      | 'publishDates'
      | 'published'
      | 'rootFolderNode'
      | 'rootFolderNodeId'
      | 'status'
      | 'summary'
      | 'title'
      | 'unpublished'
      | 'version'
    >;
    DossierCategory: WithRequired<
      {
        alias: string;
        label: string;
        requirePublishDates: boolean;
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'] | null;
        /** Format: int32 */
        wooCategoryId: number | null;
        dynamicFields: readonly components['schemas']['DynamicField'][];
        dossierCategoryDynamicFields: readonly components['schemas']['DossierCategoryDynamicField'][];
      } & components['schemas']['BaseEntity'],
      | 'alias'
      | 'created'
      | 'dossierCategoryDynamicFields'
      | 'dynamicFields'
      | 'label'
      | 'modified'
      | 'requirePublishDates'
      | 'wooCategoryId'
    >;
    DossierCategoryAddDynamicFieldCommand: {
      /** Format: uuid */
      dossierCategoryId: string;
      /** Format: uuid */
      dynamicFieldId: string;
    };
    DossierCategoryDeleteDynamicFieldCommand: {
      /** Format: uuid */
      dossierCategoryId: string;
      /** Format: uuid */
      dynamicFieldId: string;
    };
    DossierCategoryDto: {
      /** Format: uuid */
      id: string;
      alias: string;
      label: string;
      requirePublishDates: boolean;
      dynamicFields: components['schemas']['DynamicFieldDto'][];
    };
    DossierCategoryDynamicField: {
      /** Format: uuid */
      dossierCategoryId: string;
      /** Format: uuid */
      dynamicFieldId: string;
      isRequired: boolean;
      /** Format: int32 */
      order: number;
    };
    DossierCategoryDynamicFieldDto: {
      /** Format: uuid */
      id: string;
      isRequired: boolean;
    };
    DossierCategoryUpdateDynamicFieldsCommand: {
      dynamicFields: components['schemas']['DossierCategoryDynamicFieldDto'][];
    };
    DossierDto: {
      /** Format: uuid */
      id: string;
      /** Format: uuid */
      dossierId: string;
      /** Format: int32 */
      version: number;
      title: string;
      /** Format: uuid */
      categoryId: string;
      summary: string | null;
      image: string | null;
      /** @enum {unknown} */
      status: 'Draft' | 'Published' | 'Unpublished' | 'Deleted';
      publishDates: components['schemas']['PublishDateInfoDto'];
      created: components['schemas']['ModificationInfoDto'];
      modified: components['schemas']['ModificationInfoDto'] | null;
      published: components['schemas']['ModificationInfoDto'] | null;
      dossierPublished: components['schemas']['ModificationInfoDto'] | null;
      unpublished: components['schemas']['ModificationInfoDto'] | null;
      deleted: components['schemas']['ModificationInfoDto'] | null;
      rootFolderNode: components['schemas']['DossierFileStructureViewDto'] | null;
      decorativeImageNode: components['schemas']['ImageNodeDto'] | null;
      category: components['schemas']['DossierCategoryDto'];
      dynamicFieldValues: components['schemas']['DossierDynamicFieldValueDto'][];
    };
    DossierDynamicFieldValue: WithRequired<
      {
        /** Format: uuid */
        dossierId: string;
        /** Format: uuid */
        dynamicFieldId: string;
        value: unknown;
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'] | null;
        dossier: components['schemas']['Dossier'];
        dynamicField: components['schemas']['DynamicField'];
      } & components['schemas']['BaseEntity'],
      'created' | 'dossier' | 'dossierId' | 'dynamicField' | 'dynamicFieldId' | 'modified' | 'value'
    >;
    DossierDynamicFieldValueDto: {
      alias: string;
      label: string;
      /** Format: uuid */
      dynamicFieldId: string;
      value: Record<string, unknown> | null;
      isDeleted: boolean;
    };
    DossierFileStructureView: WithRequired<
      {
        id: string;
        /** Format: uuid */
        nodeId: string | null;
        /** Format: int64 */
        rank: number;
        /** Format: uuid */
        dossierId: string;
        /** Format: int32 */
        dossierVersion: number;
        nodeName: string | null;
        nodeType: components['schemas']['NodeType'] | null;
        parentId: string | null;
        /** Format: uuid */
        parentNodeId: string | null;
        blobReference: string | null;
        /** Format: int64 */
        size: number | null;
        /** Format: int32 */
        numberOfDescendantFiles: number | null;
        /** Format: int32 */
        numberOfDescendantFolders: number | null;
        /** Format: int64 */
        totalDescendantSize: number | null;
        isPublished: boolean;
        /** Format: date-time */
        publishFromDate: string;
        /** Format: date-time */
        publishToDate: string;
        /** Format: date-time */
        createdDateTime: string;
        /** Format: date-time */
        modifiedDateTime: string | null;
        dossier: components['schemas']['Dossier'];
        parent: components['schemas']['DossierFileStructureView'] | null;
        children: readonly components['schemas']['DossierFileStructureView'][] | null;
      } & components['schemas']['BaseView'],
      | 'blobReference'
      | 'children'
      | 'createdDateTime'
      | 'dossier'
      | 'dossierId'
      | 'dossierVersion'
      | 'id'
      | 'isPublished'
      | 'modifiedDateTime'
      | 'nodeId'
      | 'nodeName'
      | 'nodeType'
      | 'numberOfDescendantFiles'
      | 'numberOfDescendantFolders'
      | 'parent'
      | 'parentId'
      | 'parentNodeId'
      | 'publishFromDate'
      | 'publishToDate'
      | 'rank'
      | 'size'
      | 'totalDescendantSize'
    >;
    DossierFileStructureViewDto: {
      /** Format: uuid */
      nodeId: string;
      nodeName: string | null;
      /** @enum {unknown} */
      nodeType: 'Folder' | 'File';
      blobReference: string | null;
      /** Format: int64 */
      size: number | null;
      /** Format: int32 */
      numberOfDescendantFiles: number | null;
      /** Format: int32 */
      numberOfDescendantFolders: number | null;
      /** Format: int64 */
      totalDescendantSize: number | null;
      children: components['schemas']['DossierFileStructureViewDto'][];
    };
    DossierMetrics: {
      /** Format: int32 */
      dossierCount: number;
      /** Format: int32 */
      fileCount: number;
      /** Format: int64 */
      totalStorageConsumption: number;
    };
    DossierPagedDto: {
      /** Format: uuid */
      id: string;
      /** Format: uuid */
      dossierId: string;
      /** Format: int32 */
      version: number;
      title: string;
      /** Format: uuid */
      categoryId: string;
      categoryLabel: string;
      summary: string | null;
      created: components['schemas']['ModificationInfoDto'];
      modified: components['schemas']['ModificationInfoDto'] | null;
      publishDates: components['schemas']['PublishDateInfoDto'];
      published: components['schemas']['ModificationInfoDto'] | null;
      dossierPublished: components['schemas']['ModificationInfoDto'] | null;
      unpublished: components['schemas']['ModificationInfoDto'] | null;
      deleted: components['schemas']['ModificationInfoDto'] | null;
    };
    DossierSearchIndexView: WithRequired<
      {
        isPublished: boolean;
        /** @enum {unknown} */
        indexType: 'File' | 'Dossier';
      } & components['schemas']['BaseDossierView'],
      'indexType' | 'isPublished'
    >;
    /** @enum {string} */
    DossierStatus: 'Draft' | 'Published' | 'Unpublished' | 'Deleted';
    DossierUpdateDetail: {
      /** Format: uuid */
      dossierId: string;
      /** Format: int32 */
      version: number;
    };
    DossierView: components['schemas']['BaseDossierView'];
    DownloadDetails: {
      fileName: string | null;
      mimeType: string | null;
      /** @enum {unknown} */
      nodeType: 'Folder' | 'File';
      stream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy']
        | null;
    };
    DownloadDossierDecorativeImageQuery: {
      /** Format: uuid */
      dossierId: string;
    };
    DownloadImageQueryResponse: WithRequired<
      {
        downloadDetails: components['schemas']['DownloadDetails'];
      } & components['schemas']['BaseResponse'],
      'downloadDetails'
    >;
    DownloadRecord: WithRequired<
      {
        ipAddress: string;
        /** Format: int64 */
        downloadedBytes: number;
        /** Format: date-time */
        dateTime: string;
        /** @enum {unknown} */
        downloadRecordType: 'AuthorizedNodeDownload' | 'PublicNodeDownload' | 'PublicZipDownload';
      } & components['schemas']['BaseEntity'],
      'dateTime' | 'downloadedBytes' | 'downloadRecordType' | 'ipAddress'
    >;
    /** @enum {string} */
    DownloadRecordType: 'AuthorizedNodeDownload' | 'PublicNodeDownload' | 'PublicZipDownload';
    DownloadSharePointFileDetails: {
      uniqueId: string;
    };
    DownloadTokenResponse: WithRequired<
      {
        token: string;
      } & components['schemas']['BaseResponse'],
      'token'
    >;
    DynamicField: WithRequired<
      {
        alias: string;
        label: string;
        helpText: string;
        /** @enum {unknown} */
        dynamicFieldType:
          | 'Text'
          | 'Integer'
          | 'Decimal'
          | 'Date'
          | 'Select'
          | 'RadioButton'
          | 'CheckBox'
          | 'TextArea'
          | 'StringList'
          | 'RichText'
          | 'DateTimeRange';
        defaultValue: unknown;
        options: {
          [key: string]: string | undefined;
        } | null;
        created: components['schemas']['ModificationInfo'];
        deleted: components['schemas']['ModificationInfo'] | null;
        modified: components['schemas']['ModificationInfo'] | null;
        validationRules: readonly (
          | components['schemas']['RequiredPropertyDynamicValidationRule']
          | components['schemas']['ValidateDateDynamicValidationRule']
          | components['schemas']['ValidateDateTimeRangeDynamicValidationRule']
          | components['schemas']['ValidateFutureDateDynamicValidationRule']
          | components['schemas']['ValidateGuidDynamicValidationRule']
          | components['schemas']['ValidateIpAddressDynamicValidationRule']
          | components['schemas']['ValidateNumberRangeDynamicValidationRule']
          | components['schemas']['ValidatePastDateDynamicValidationRule']
          | components['schemas']['ValidateStringLengthDynamicValidationRule']
        )[];
      } & components['schemas']['BaseEntity'],
      | 'alias'
      | 'created'
      | 'defaultValue'
      | 'deleted'
      | 'dynamicFieldType'
      | 'helpText'
      | 'label'
      | 'modified'
      | 'options'
      | 'validationRules'
    >;
    DynamicFieldDto: {
      /** Format: uuid */
      id: string;
      alias: string;
      label: string;
      helpText: string;
      /** @enum {unknown} */
      dynamicFieldType:
        | 'Text'
        | 'Integer'
        | 'Decimal'
        | 'Date'
        | 'Select'
        | 'RadioButton'
        | 'CheckBox'
        | 'TextArea'
        | 'StringList'
        | 'RichText'
        | 'DateTimeRange';
      defaultValue: unknown;
      options: {
        [key: string]: string | undefined;
      } | null;
      isRequired: boolean;
      isDeleted: boolean;
      validationRules: components['schemas']['DynamicFieldValidationRuleDto'][] | null;
    };
    DynamicFieldRule: Record<string, never>;
    /** @enum {string} */
    DynamicFieldType:
      | 'Text'
      | 'Integer'
      | 'Decimal'
      | 'Date'
      | 'Select'
      | 'RadioButton'
      | 'CheckBox'
      | 'TextArea'
      | 'StringList'
      | 'RichText'
      | 'DateTimeRange';
    DynamicFieldValidationRule: WithRequired<
      {
        /** Format: uuid */
        dynamicFieldId: string;
        /** @enum {unknown} */
        ruleType:
          | 'ValidateGuid'
          | 'RequiredProperty'
          | 'ValidateNumberRange'
          | 'ValidateStringLength'
          | 'ValidateDate'
          | 'ValidatePastDate'
          | 'ValidateFutureDate'
          | 'ValidateIpAddress'
          | 'ValidateDateTimeRange';
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'] | null;
      } & components['schemas']['BaseEntity'],
      'created' | 'dynamicFieldId' | 'fieldRuleValue' | 'modified' | 'ruleType' | 'ruleValue'
    >;
    DynamicFieldValidationRuleDto: {
      /** Format: uuid */
      id: string;
      /** Format: uuid */
      dynamicFieldId: string;
      /** @enum {unknown} */
      ruleType:
        | 'ValidateGuid'
        | 'RequiredProperty'
        | 'ValidateNumberRange'
        | 'ValidateStringLength'
        | 'ValidateDate'
        | 'ValidatePastDate'
        | 'ValidateFutureDate'
        | 'ValidateIpAddress'
        | 'ValidateDateTimeRange';
      fieldRuleValue:
        | components['schemas']['RequiredPropertyDynamicFieldRule']
        | components['schemas']['ValidateDateDynamicFieldRule']
        | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
        | components['schemas']['ValidateFutureDateDynamicFieldRule']
        | components['schemas']['ValidateGuidDynamicFieldRule']
        | components['schemas']['ValidateIpAddressDynamicFieldRule']
        | components['schemas']['ValidateNumberRangeDynamicFieldRule']
        | components['schemas']['ValidatePastDateDynamicFieldRule']
        | components['schemas']['ValidateStringLengthDynamicFieldRule']
        | null;
    };
    /** @enum {string} */
    DynamicFieldValidationRuleType:
      | 'ValidateGuid'
      | 'RequiredProperty'
      | 'ValidateNumberRange'
      | 'ValidateStringLength'
      | 'ValidateDate'
      | 'ValidatePastDate'
      | 'ValidateFutureDate'
      | 'ValidateIpAddress'
      | 'ValidateDateTimeRange';
    DynamicMethod: WithRequired<
      {
        name: string;
        declaringType: string | null;
        reflectedType: string | null;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder'];
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        isSecurityCritical: boolean;
        isSecuritySafeCritical: boolean;
        isSecurityTransparent: boolean;
        returnType: string;
        returnParameter: components['schemas']['ParameterInfo'] | components['schemas']['RuntimeParameterInfo'];
        returnTypeCustomAttributes: components['schemas']['ICustomAttributeProvider'];
        initLocals: boolean;
      } & components['schemas']['MethodInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'declaringType'
      | 'initLocals'
      | 'isSecurityCritical'
      | 'isSecuritySafeCritical'
      | 'isSecurityTransparent'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
      | 'returnParameter'
      | 'returnType'
      | 'returnTypeCustomAttributes'
    >;
    ECAlgorithm: components['schemas']['AsymmetricAlgorithm'];
    ECDiffieHellman: WithRequired<
      {
        keyExchangeAlgorithm: string;
        signatureAlgorithm: string | null;
        publicKey:
          | components['schemas']['ECDiffieHellmanCngPublicKey']
          | components['schemas']['ECDiffieHellmanPublicKeyWrapper'];
      } & components['schemas']['ECAlgorithm'],
      'keyExchangeAlgorithm' | 'publicKey' | 'signatureAlgorithm'
    >;
    ECDiffieHellmanCng: WithRequired<
      {
        /** Format: int32 */
        keySize: number;
        legalKeySizes: readonly components['schemas']['KeySizes'][];
        hashAlgorithm: components['schemas']['CngAlgorithm'];
        /** @enum {unknown} */
        keyDerivationFunction: 'Hash' | 'Hmac' | 'Tls';
        /** Format: byte */
        hmacKey: string | null;
        /** Format: byte */
        label: string | null;
        /** Format: byte */
        secretAppend: string | null;
        /** Format: byte */
        secretPrepend: string | null;
        /** Format: byte */
        seed: string | null;
        useSecretAgreementAsHmacKey: boolean;
        publicKey:
          | components['schemas']['ECDiffieHellmanCngPublicKey']
          | components['schemas']['ECDiffieHellmanPublicKeyWrapper'];
        key: components['schemas']['CngKey'];
      } & components['schemas']['ECDiffieHellman'],
      | 'hashAlgorithm'
      | 'hmacKey'
      | 'key'
      | 'keyDerivationFunction'
      | 'keySize'
      | 'label'
      | 'legalKeySizes'
      | 'publicKey'
      | 'secretAppend'
      | 'secretPrepend'
      | 'seed'
      | 'useSecretAgreementAsHmacKey'
    >;
    ECDiffieHellmanCngPublicKey: WithRequired<
      {
        blobFormat: components['schemas']['CngKeyBlobFormat'];
      } & components['schemas']['ECDiffieHellmanPublicKey'],
      'blobFormat'
    >;
    /** @enum {string} */
    ECDiffieHellmanKeyDerivationFunction: 'Hash' | 'Hmac' | 'Tls';
    ECDiffieHellmanOpenSsl: WithRequired<
      {
        publicKey:
          | components['schemas']['ECDiffieHellmanCngPublicKey']
          | components['schemas']['ECDiffieHellmanPublicKeyWrapper'];
      } & components['schemas']['ECDiffieHellman'],
      'publicKey'
    >;
    ECDiffieHellmanPublicKey: Record<string, never>;
    ECDiffieHellmanPublicKeyWrapper: components['schemas']['ECDiffieHellmanPublicKey'];
    ECDiffieHellmanWrapper: WithRequired<
      {
        keyExchangeAlgorithm: string | null;
        signatureAlgorithm: string | null;
        publicKey:
          | components['schemas']['ECDiffieHellmanCngPublicKey']
          | components['schemas']['ECDiffieHellmanPublicKeyWrapper']
          | null;
        /** Format: int32 */
        keySize: number;
        legalKeySizes: readonly components['schemas']['KeySizes'][] | null;
      } & components['schemas']['ECDiffieHellman'],
      'keyExchangeAlgorithm' | 'keySize' | 'legalKeySizes' | 'publicKey' | 'signatureAlgorithm'
    >;
    ECDsa: WithRequired<
      {
        keyExchangeAlgorithm: string | null;
        signatureAlgorithm: string;
      } & components['schemas']['ECAlgorithm'],
      'keyExchangeAlgorithm' | 'signatureAlgorithm'
    >;
    ECDsaCng: WithRequired<
      {
        /** Format: int32 */
        keySize: number;
        legalKeySizes: readonly components['schemas']['KeySizes'][];
        hashAlgorithm: components['schemas']['CngAlgorithm'];
        key: components['schemas']['CngKey'];
      } & components['schemas']['ECDsa'],
      'hashAlgorithm' | 'key' | 'keySize' | 'legalKeySizes'
    >;
    ECDsaOpenSsl: components['schemas']['ECDsa'];
    ECDsaWrapper: WithRequired<
      {
        keyExchangeAlgorithm: string | null;
        /** Format: int32 */
        keySize: number;
        legalKeySizes: readonly components['schemas']['KeySizes'][] | null;
        signatureAlgorithm: string | null;
      } & components['schemas']['ECDsa'],
      'keyExchangeAlgorithm' | 'keySize' | 'legalKeySizes' | 'signatureAlgorithm'
    >;
    /** @enum {string} */
    EmptyBodyBehavior: 'Default' | 'Allow' | 'Disallow';
    EmptyResult: components['schemas']['ActionResult'];
    EncoderExceptionFallback: WithRequired<
      {
        /** Format: int32 */
        maxCharCount: number;
      } & components['schemas']['EncoderFallback'],
      'maxCharCount'
    >;
    EncoderFallback: {
      /** Format: int32 */
      maxCharCount: number;
    };
    EncoderLatin1BestFitFallback: WithRequired<
      {
        /** Format: int32 */
        maxCharCount: number;
      } & components['schemas']['EncoderFallback'],
      'maxCharCount'
    >;
    EncoderReplacementFallback: WithRequired<
      {
        defaultString: string;
        /** Format: int32 */
        maxCharCount: number;
      } & components['schemas']['EncoderFallback'],
      'defaultString' | 'maxCharCount'
    >;
    Encoding: {
      bodyName: string;
      encodingName: string;
      headerName: string;
      webName: string;
      /** Format: int32 */
      windowsCodePage: number;
      isBrowserDisplay: boolean;
      isBrowserSave: boolean;
      isMailNewsDisplay: boolean;
      isMailNewsSave: boolean;
      isSingleByte: boolean;
      encoderFallback:
        | components['schemas']['EncoderExceptionFallback']
        | components['schemas']['EncoderLatin1BestFitFallback']
        | components['schemas']['EncoderReplacementFallback'];
      decoderFallback:
        | components['schemas']['DecoderExceptionFallback']
        | components['schemas']['DecoderReplacementFallback']
        | components['schemas']['DecoderUTF7Fallback'];
      isReadOnly: boolean;
      /** Format: int32 */
      codePage: number;
    };
    /** @enum {string} */
    EventAttributes: 'None' | 'SpecialName' | 'RTSpecialName' | 'ReservedMask';
    EventInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        /** @enum {unknown} */
        attributes: 'None' | 'SpecialName' | 'RTSpecialName' | 'ReservedMask';
        isSpecialName: boolean;
        addMethod:
          | components['schemas']['RuntimeMethodInfo']
          | components['schemas']['DynamicMethod']
          | components['schemas']['RuntimeMethodBuilder']
          | components['schemas']['SymbolMethod']
          | components['schemas']['MethodBuilder']
          | components['schemas']['MethodBuilderInstantiation']
          | components['schemas']['MethodOnTypeBuilderInstantiation']
          | null;
        removeMethod:
          | components['schemas']['RuntimeMethodInfo']
          | components['schemas']['DynamicMethod']
          | components['schemas']['RuntimeMethodBuilder']
          | components['schemas']['SymbolMethod']
          | components['schemas']['MethodBuilder']
          | components['schemas']['MethodBuilderInstantiation']
          | components['schemas']['MethodOnTypeBuilderInstantiation']
          | null;
        raiseMethod:
          | components['schemas']['RuntimeMethodInfo']
          | components['schemas']['DynamicMethod']
          | components['schemas']['RuntimeMethodBuilder']
          | components['schemas']['SymbolMethod']
          | components['schemas']['MethodBuilder']
          | components['schemas']['MethodBuilderInstantiation']
          | components['schemas']['MethodOnTypeBuilderInstantiation']
          | null;
        isMulticast: boolean;
        eventHandlerType: string | null;
      } & components['schemas']['MemberInfo'],
      | 'addMethod'
      | 'attributes'
      | 'eventHandlerType'
      | 'isMulticast'
      | 'isSpecialName'
      | 'memberType'
      | 'raiseMethod'
      | 'removeMethod'
    >;
    EventWaitHandle: components['schemas']['WaitHandle'];
    ExceptionContext: WithRequired<
      {
        exception: unknown;
        exceptionDispatchInfo: components['schemas']['ExceptionDispatchInfo'] | null;
        exceptionHandled: boolean;
        result: components['schemas']['IActionResult'] | null;
      } & components['schemas']['FilterContext'],
      'exception' | 'exceptionDispatchInfo' | 'exceptionHandled' | 'result'
    >;
    ExceptionDispatchInfo: {
      sourceException: unknown;
    };
    ExecutionContext: Record<string, never>;
    /** @enum {string} */
    FieldAttributes:
      | 'PrivateScope'
      | 'Private'
      | 'FamANDAssem'
      | 'Assembly'
      | 'Family'
      | 'FamORAssem'
      | 'Public'
      | 'FieldAccessMask'
      | 'Static'
      | 'InitOnly'
      | 'Literal'
      | 'NotSerialized'
      | 'HasFieldRVA'
      | 'SpecialName'
      | 'RTSpecialName'
      | 'HasFieldMarshal'
      | 'PinvokeImpl'
      | 'HasDefault'
      | 'ReservedMask';
    FieldBuilder: components['schemas']['FieldInfo'];
    FieldInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'FieldAccessMask'
          | 'Static'
          | 'InitOnly'
          | 'Literal'
          | 'NotSerialized'
          | 'HasFieldRVA'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasFieldMarshal'
          | 'PinvokeImpl'
          | 'HasDefault'
          | 'ReservedMask';
        fieldType: string;
        isInitOnly: boolean;
        isLiteral: boolean;
        /** @deprecated */
        isNotSerialized: boolean;
        isPinvokeImpl: boolean;
        isSpecialName: boolean;
        isStatic: boolean;
        isAssembly: boolean;
        isFamily: boolean;
        isFamilyAndAssembly: boolean;
        isFamilyOrAssembly: boolean;
        isPrivate: boolean;
        isPublic: boolean;
        isSecurityCritical: boolean;
        isSecuritySafeCritical: boolean;
        isSecurityTransparent: boolean;
        fieldHandle: components['schemas']['RuntimeFieldHandle'];
      } & components['schemas']['MemberInfo'],
      | 'attributes'
      | 'fieldHandle'
      | 'fieldType'
      | 'isAssembly'
      | 'isFamily'
      | 'isFamilyAndAssembly'
      | 'isFamilyOrAssembly'
      | 'isInitOnly'
      | 'isLiteral'
      | 'isNotSerialized'
      | 'isPinvokeImpl'
      | 'isPrivate'
      | 'isPublic'
      | 'isSecurityCritical'
      | 'isSecuritySafeCritical'
      | 'isSecurityTransparent'
      | 'isSpecialName'
      | 'isStatic'
      | 'memberType'
    >;
    FieldOnTypeBuilderInstantiation: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        fieldHandle: components['schemas']['RuntimeFieldHandle'];
        fieldType: string | null;
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'FieldAccessMask'
          | 'Static'
          | 'InitOnly'
          | 'Literal'
          | 'NotSerialized'
          | 'HasFieldRVA'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasFieldMarshal'
          | 'PinvokeImpl'
          | 'HasDefault'
          | 'ReservedMask';
      } & components['schemas']['FieldInfo'],
      | 'attributes'
      | 'declaringType'
      | 'fieldHandle'
      | 'fieldType'
      | 'memberType'
      | 'metadataToken'
      | 'module'
      | 'name'
      | 'reflectedType'
    >;
    /** @enum {string} */
    FileAttributes:
      | 'None'
      | 'ReadOnly'
      | 'Hidden'
      | 'System'
      | 'Directory'
      | 'Archive'
      | 'Device'
      | 'Normal'
      | 'Temporary'
      | 'SparseFile'
      | 'ReparsePoint'
      | 'Compressed'
      | 'Offline'
      | 'NotContentIndexed'
      | 'Encrypted'
      | 'IntegrityStream'
      | 'NoScrubData';
    FileCreatedEvent: {
      fullName: string;
      name: string;
      fileType: string;
      /** Format: int64 */
      size: number;
      hash: string;
      blobReference: string;
    };
    FileInfo: WithRequired<
      {
        name: string;
        /** Format: int64 */
        length: number;
        directoryName: string | null;
        directory: components['schemas']['DirectoryInfo'] | null;
        isReadOnly: boolean;
        exists: boolean;
      } & components['schemas']['FileSystemInfo'],
      'directory' | 'directoryName' | 'exists' | 'isReadOnly' | 'length' | 'name'
    >;
    FileNode: WithRequired<
      {
        fileType: string;
        /** Format: int64 */
        size: number;
        hash: string;
        blobReference: string;
        /** @enum {unknown} */
        nodeType: 'Folder' | 'File';
      } & components['schemas']['Node'],
      'blobReference' | 'fileType' | 'hash' | 'nodeType' | 'size'
    >;
    FileNodeDto: {
      /** Format: uuid */
      id: string;
      fullName: string;
      name: string;
    };
    FileNodeUpdateDynamicFieldsCommand: {
      dynamicFields: string[];
    };
    FileSearchIndexView: WithRequired<
      {
        fileType: string;
        nodeHash: string;
        blobReference: string;
        /** Format: int64 */
        size: number;
        isPublished: boolean;
        /** @enum {unknown} */
        indexType: 'File' | 'Dossier';
      } & components['schemas']['BaseFileView'],
      'blobReference' | 'fileType' | 'indexType' | 'isPublished' | 'nodeHash' | 'size'
    >;
    FileStream: WithRequired<
      {
        /** @deprecated */
        handle: unknown;
        canRead: boolean;
        canWrite: boolean;
        safeFileHandle: components['schemas']['SafeFileHandle'];
        name: string;
        isAsync: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
        canSeek: boolean;
      } & components['schemas']['Stream'],
      'canRead' | 'canSeek' | 'canWrite' | 'handle' | 'isAsync' | 'length' | 'name' | 'position' | 'safeFileHandle'
    >;
    FileStreamStrategy: components['schemas']['Stream'];
    FileSystemInfo: WithRequired<
      {
        fullName: string;
        extension: string;
        name: string;
        exists: boolean;
        /** Format: date-time */
        creationTime: string;
        /** Format: date-time */
        creationTimeUtc: string;
        /** Format: date-time */
        lastAccessTime: string;
        /** Format: date-time */
        lastAccessTimeUtc: string;
        /** Format: date-time */
        lastWriteTime: string;
        /** Format: date-time */
        lastWriteTimeUtc: string;
        linkTarget: string | null;
        /** @enum {unknown} */
        unixFileMode:
          | 'None'
          | 'OtherExecute'
          | 'OtherWrite'
          | 'OtherRead'
          | 'GroupExecute'
          | 'GroupWrite'
          | 'GroupRead'
          | 'UserExecute'
          | 'UserWrite'
          | 'UserRead'
          | 'StickyBit'
          | 'SetGroup'
          | 'SetUser';
        /** @enum {unknown} */
        attributes:
          | 'None'
          | 'ReadOnly'
          | 'Hidden'
          | 'System'
          | 'Directory'
          | 'Archive'
          | 'Device'
          | 'Normal'
          | 'Temporary'
          | 'SparseFile'
          | 'ReparsePoint'
          | 'Compressed'
          | 'Offline'
          | 'NotContentIndexed'
          | 'Encrypted'
          | 'IntegrityStream'
          | 'NoScrubData';
      } & components['schemas']['MarshalByRefObject'],
      | 'attributes'
      | 'creationTime'
      | 'creationTimeUtc'
      | 'exists'
      | 'extension'
      | 'fullName'
      | 'lastAccessTime'
      | 'lastAccessTimeUtc'
      | 'lastWriteTime'
      | 'lastWriteTimeUtc'
      | 'linkTarget'
      | 'name'
      | 'unixFileMode'
    >;
    FileSystemView: WithRequired<
      {
        /** Format: uuid */
        parentNodeId: string | null;
        /** Format: uuid */
        dossierEntityId: string;
        fileType: string | null;
        nodeHash: string | null;
        blobReference: string | null;
        /** Format: int64 */
        size: number | null;
        /** @enum {unknown} */
        nodeType: 'Folder' | 'File';
      } & components['schemas']['BaseFileView'],
      'blobReference' | 'dossierEntityId' | 'fileType' | 'nodeHash' | 'nodeType' | 'parentNodeId' | 'size'
    >;
    FilterContext: WithRequired<
      {
        filters: components['schemas']['IFilterMetadata'][];
      } & components['schemas']['ActionContext'],
      'filters'
    >;
    FilterDescriptor: {
      filter: components['schemas']['IFilterMetadata'];
      /** Format: int32 */
      order: number;
      /** Format: int32 */
      scope: number;
    };
    FolderCreatedEvent: {
      fullName: string;
      name: string;
    };
    FolderNode: WithRequired<
      {
        /** Format: int32 */
        numberOfDescendantFiles: number;
        /** Format: int32 */
        numberOfDescendantFolders: number;
        /** Format: int64 */
        totalDescendantSize: number;
        /** @enum {unknown} */
        nodeType: 'Folder' | 'File';
      } & components['schemas']['Node'],
      'nodeType' | 'numberOfDescendantFiles' | 'numberOfDescendantFolders' | 'totalDescendantSize'
    >;
    FolderNodeDto: {
      /** Format: uuid */
      id: string;
      fullName: string;
      name: string;
    };
    FontAsset: WithRequired<
      {
        /** @enum {unknown} */
        siteAssetType: 'Image' | 'Font';
        /** @enum {unknown} */
        fontCategory: 'Heading' | 'Text' | 'Footer' | 'Undefined';
        fontFamily: string;
        /** @enum {unknown} */
        fontWeight: 'W100' | 'W200' | 'W300' | 'W400' | 'W500' | 'W600' | 'W700' | 'W800' | 'W900' | 'Undefined';
        /** @enum {unknown} */
        fontStyle: 'Normal' | 'Italic' | 'Oblique' | 'Initial' | 'Inherit' | 'Undefined';
        /** Format: uuid */
        rootFolderNodeId: string | null;
        rootFolderNode: components['schemas']['FolderNode'] | null;
        fontFiles: readonly (components['schemas']['FileNode'] | components['schemas']['ImageNode'])[];
        descriptors: readonly components['schemas']['Descriptor'][];
      } & components['schemas']['Asset'],
      | 'descriptors'
      | 'fontCategory'
      | 'fontFamily'
      | 'fontFiles'
      | 'fontStyle'
      | 'fontWeight'
      | 'rootFolderNode'
      | 'rootFolderNodeId'
      | 'siteAssetType'
    >;
    FontAssetDto: {
      /** Format: uuid */
      id: string;
      /** @enum {unknown} */
      fontCategory: 'Heading' | 'Text' | 'Footer' | 'Undefined';
      fontFamily: string;
      /** @enum {unknown} */
      fontWeight: 'W100' | 'W200' | 'W300' | 'W400' | 'W500' | 'W600' | 'W700' | 'W800' | 'W900' | 'Undefined';
      /** @enum {unknown} */
      fontStyle: 'Normal' | 'Italic' | 'Oblique' | 'Initial' | 'Inherit' | 'Undefined';
      rootFolderNode: components['schemas']['FolderNodeDto'];
      fontFiles: (components['schemas']['FileNodeDto'] | components['schemas']['ImageNodeDto'])[];
      descriptors: components['schemas']['DescriptorDto'][];
    };
    /** @enum {string} */
    FontCategory: 'Heading' | 'Text' | 'Footer' | 'Undefined';
    /** @enum {string} */
    FontStyle: 'Normal' | 'Italic' | 'Oblique' | 'Initial' | 'Inherit' | 'Undefined';
    /** @enum {string} */
    FontWeight: 'W100' | 'W200' | 'W300' | 'W400' | 'W500' | 'W600' | 'W700' | 'W800' | 'W900' | 'Undefined';
    FooterComponent: WithRequired<
      {
        /** @enum {unknown} */
        pageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
        pageZones: readonly components['schemas']['PageZone'][];
        breakOnZoneValidationError: boolean;
      } & components['schemas']['Page'],
      'breakOnZoneValidationError' | 'pageType' | 'pageZones'
    >;
    FooterComponentDto: components['schemas']['PageDto'];
    ForbidResult: WithRequired<
      {
        authenticationSchemes: string[];
        properties: components['schemas']['AuthenticationProperties'] | null;
      } & components['schemas']['ActionResult'],
      'authenticationSchemes' | 'properties'
    >;
    Gen2GcCallback: components['schemas']['CriticalFinalizerObject'];
    GenericIdentity: WithRequired<
      {
        claims: readonly components['schemas']['Claim'][];
        name: string;
        authenticationType: string;
        isAuthenticated: boolean;
      } & components['schemas']['ClaimsIdentity'],
      'authenticationType' | 'claims' | 'isAuthenticated' | 'name'
    >;
    GenericPrincipal: WithRequired<
      {
        identity: components['schemas']['IIdentity'];
      } & components['schemas']['ClaimsPrincipal'],
      'identity'
    >;
    GetAllDossierCategoriesResponse: WithRequired<
      {
        dossierCategories: components['schemas']['DossierCategoryDto'][];
      } & components['schemas']['BaseResponse'],
      'dossierCategories'
    >;
    GetAllDynamicFieldResponse: WithRequired<
      {
        fieldDefinitions: components['schemas']['DynamicFieldDto'][];
      } & components['schemas']['BaseResponse'],
      'fieldDefinitions'
    >;
    GetAllFontAssetsResponse: {
      fontAssets: components['schemas']['FontAssetDto'][];
    };
    GetAllPagesResponse: WithRequired<
      {
        pages: (
          | components['schemas']['AnnouncementPageDto']
          | components['schemas']['FooterComponentDto']
          | components['schemas']['HomePageDto']
          | components['schemas']['SearchPageDto']
          | components['schemas']['SimplePageDto']
        )[];
      } & components['schemas']['BaseResponse'],
      'pages'
    >;
    GetDossierAllVersionsQuery: {
      /** Format: uuid */
      dossierId: string;
    };
    GetDossierAllVersionsResponse: WithRequired<
      {
        dossiers: components['schemas']['DossierDto'][];
      } & components['schemas']['BaseResponse'],
      'dossiers'
    >;
    GetDossierCategoryDynamicFieldsResponse: WithRequired<
      {
        dynamicFields: readonly components['schemas']['DossierCategoryDynamicFieldDto'][];
      } & components['schemas']['BaseResponse'],
      'dynamicFields'
    >;
    GetDossierCategoryResponse: WithRequired<
      {
        dossierCategory: components['schemas']['DossierCategoryDto'];
      } & components['schemas']['BaseResponse'],
      'dossierCategory'
    >;
    GetDossierEntryQuery: {
      /** Format: uuid */
      entryId: string;
    };
    GetDossierEntryResponse: WithRequired<
      {
        dossier: components['schemas']['DossierDto'];
      } & components['schemas']['BaseResponse'],
      'dossier'
    >;
    GetDossierMetricsQuery: {
      filter: string;
    };
    GetDossierMetricsResponse: WithRequired<
      {
        dossierMetrics: components['schemas']['DossierMetrics'];
      } & components['schemas']['BaseResponse'],
      'dossierMetrics'
    >;
    GetDynamicFieldResponse: WithRequired<
      {
        fieldDefinition: components['schemas']['DynamicFieldDto'];
      } & components['schemas']['BaseResponse'],
      'fieldDefinition'
    >;
    GetFileNodeDynamicFieldsResponse: WithRequired<
      {
        dynamicFields: readonly components['schemas']['DynamicFieldDto'][];
      } & components['schemas']['BaseResponse'],
      'dynamicFields'
    >;
    GetFontAssetResponse: {
      fontAsset: components['schemas']['FontAssetDto'] | null;
    };
    GetFontAssetUploadedFontsResponse: {
      fontAsset: components['schemas']['FontAssetDto'] | null;
      notUploadedFiles: components['schemas']['NotUploadedFile'][];
    };
    GetLatestDossierVersionResponse: WithRequired<
      {
        dossier: components['schemas']['DossierDto'];
      } & components['schemas']['BaseResponse'],
      'dossier'
    >;
    GetLatestPublicDossierResponse: {
      dossier: components['schemas']['PublicDossierDto'];
    };
    GetLatestPublishedDossierResponse: WithRequired<
      {
        dossier: components['schemas']['DossierDto'];
      } & components['schemas']['BaseResponse'],
      'dossier'
    >;
    GetLoggedInUserResponse: WithRequired<
      {
        user: components['schemas']['UserDto'];
      } & components['schemas']['BaseResponse'],
      'user'
    >;
    GetNavigationStructureResponse: WithRequired<
      {
        navigation: components['schemas']['NavigationItemDto'][];
        pages: components['schemas']['NavigationPageDto'][];
      } & components['schemas']['BaseResponse'],
      'navigation' | 'pages'
    >;
    GetNodeChildrenQuery: {
      /** Format: uuid */
      folderNodeId: string;
    };
    GetNodeChildrenResponse: WithRequired<
      {
        nodes: components['schemas']['DossierFileStructureViewDto'][];
      } & components['schemas']['BaseResponse'],
      'nodes'
    >;
    GetPageListResponse: WithRequired<
      {
        pages: (
          | components['schemas']['AnnouncementPageDto']
          | components['schemas']['FooterComponentDto']
          | components['schemas']['HomePageDto']
          | components['schemas']['SearchPageDto']
          | components['schemas']['SimplePageDto']
        )[];
      } & components['schemas']['BaseResponse'],
      'pages'
    >;
    GetPageResponse: WithRequired<
      {
        page:
          | components['schemas']['AnnouncementPageDto']
          | components['schemas']['FooterComponentDto']
          | components['schemas']['HomePageDto']
          | components['schemas']['SearchPageDto']
          | components['schemas']['SimplePageDto'];
      } & components['schemas']['BaseResponse'],
      'page'
    >;
    GetPagedDossierResponse: WithRequired<
      {
        /** Format: int32 */
        start: number;
        /** Format: int32 */
        count: number;
        /** Format: int32 */
        itemCount: number;
        /** Format: int32 */
        totalCount: number;
        items: components['schemas']['DossierPagedDto'][];
      } & components['schemas']['BaseResponse'],
      'count' | 'itemCount' | 'items' | 'start' | 'totalCount'
    >;
    GetPagedNodeChildrenQuery: {
      /** Format: uuid */
      folderNodeId: string;
      /** Format: int32 */
      count: number;
      /** Format: int32 */
      start: number;
    };
    GetPagedNodeChildrenResponse: WithRequired<
      {
        /** Format: int32 */
        start: number;
        /** Format: int32 */
        count: number;
        /** Format: int32 */
        itemCount: number;
        /** Format: int32 */
        totalCount: number;
        items: components['schemas']['DossierFileStructureViewDto'][];
      } & components['schemas']['BaseResponse'],
      'count' | 'itemCount' | 'items' | 'start' | 'totalCount'
    >;
    GetPagedPagesResponse: WithRequired<
      {
        /** Format: int32 */
        start: number;
        /** Format: int32 */
        count: number;
        /** Format: int32 */
        itemCount: number;
        /** Format: int32 */
        totalCount: number;
        items: (
          | components['schemas']['AnnouncementPageDto']
          | components['schemas']['FooterComponentDto']
          | components['schemas']['HomePageDto']
          | components['schemas']['SearchPageDto']
          | components['schemas']['SimplePageDto']
        )[];
      } & components['schemas']['BaseResponse'],
      'count' | 'itemCount' | 'items' | 'start' | 'totalCount'
    >;
    GetPublicDossierFileStructureResponse: WithRequired<
      {
        nodes: components['schemas']['PublicDossierFileStructureViewDto'][];
      } & components['schemas']['BaseResponse'],
      'nodes'
    >;
    GetPublishedPageBySlugResponse: {
      page:
        | components['schemas']['AnnouncementPageDto']
        | components['schemas']['FooterComponentDto']
        | components['schemas']['HomePageDto']
        | components['schemas']['SearchPageDto']
        | components['schemas']['SimplePageDto'];
    };
    GetPublishedPageListResponse: {
      pageList: (
        | components['schemas']['AnnouncementPageDto']
        | components['schemas']['FooterComponentDto']
        | components['schemas']['HomePageDto']
        | components['schemas']['PageDto']
        | components['schemas']['SearchPageDto']
        | components['schemas']['SimplePageDto']
      )[];
    };
    GetSecuritySettingsResponse: {
      settings: components['schemas']['SecuritySettingsDto'];
      /** Format: date-time */
      expirationDate: string;
    };
    GetSiteAssetsResponse: {
      siteAssets: components['schemas']['SiteAssetsDto'];
    };
    GetSiteParametersSettingsResponse: {
      siteParametersSettings: components['schemas']['SiteParametersSettingsDto'];
    };
    GetSitemapSettingsResponse: {
      sitemapSettings: components['schemas']['SitemapSettingsDto'];
    };
    GetTopLevelNodesQuery: {
      /** Format: uuid */
      dossierId: string;
    };
    GetTopLevelNodesResponse: WithRequired<
      {
        node: components['schemas']['DossierFileStructureViewDto'];
      } & components['schemas']['BaseResponse'],
      'node'
    >;
    GetUserRolesResponse: WithRequired<
      {
        roles: components['schemas']['RoleDto'][];
      } & components['schemas']['BaseResponse'],
      'roles'
    >;
    GetUserRolesWithPermissionsResponse: {
      roles: readonly components['schemas']['RoleWithPermissionsType'][];
    };
    GetWebStatsSettingsResponse: {
      webStatsSettings: components['schemas']['WebStatsSettingsDto'];
    };
    GetWooCategoriesResponse: WithRequired<
      {
        wooCategories: components['schemas']['WooCategoryDto'][];
      } & components['schemas']['BaseResponse'],
      'wooCategories'
    >;
    HomePage: WithRequired<
      {
        /** @enum {unknown} */
        pageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
        pageZones: readonly components['schemas']['PageZone'][];
        breakOnZoneValidationError: boolean;
      } & components['schemas']['Page'],
      'breakOnZoneValidationError' | 'pageType' | 'pageZones'
    >;
    HomePageDto: components['schemas']['PageDto'];
    HostString: {
      value: string;
      hasValue: boolean;
      host: string;
      /** Format: int32 */
      port: number | null;
    };
    HttpActionResult: WithRequired<
      {
        result: components['schemas']['IResult'];
      } & components['schemas']['ActionResult'],
      'result'
    >;
    HttpContext: {
      features: readonly components['schemas']['TypeObjectKeyValuePair'][];
      request: components['schemas']['HttpRequest'];
      response: components['schemas']['HttpResponse'];
      connection: components['schemas']['ConnectionInfo'];
      webSockets: components['schemas']['WebSocketManager'];
      user: components['schemas']['ClaimsPrincipal'] | components['schemas']['GenericPrincipal'];
      items: {
        [key: string]: unknown;
      };
      requestServices: components['schemas']['IServiceProvider'];
      requestAborted: components['schemas']['CancellationToken'];
      traceIdentifier: string;
      session: components['schemas']['ISession'];
    };
    HttpRequest: {
      httpContext: components['schemas']['HttpContext'];
      method: string;
      scheme: string;
      isHttps: boolean;
      host: components['schemas']['HostString'];
      pathBase: string;
      path: string;
      queryString: components['schemas']['QueryString'];
      query: components['schemas']['StringStringValuesKeyValuePair'][];
      protocol: string;
      headers: {
        [key: string]: string[] | undefined;
      };
      cookies: components['schemas']['StringStringKeyValuePair'][];
      /** Format: int64 */
      contentLength: number | null;
      contentType: string | null;
      body:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy'];
      bodyReader:
        | components['schemas']['SequencePipeReader']
        | components['schemas']['StreamPipeReader']
        | components['schemas']['DefaultPipeReader'];
      hasFormContentType: boolean;
      form: components['schemas']['StringStringValuesKeyValuePair'][];
      routeValues: {
        [key: string]: unknown;
      };
    };
    HttpResponse: {
      httpContext: components['schemas']['HttpContext'];
      /** Format: int32 */
      statusCode: number;
      headers: {
        [key: string]: string[] | undefined;
      };
      body:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy'];
      bodyWriter: components['schemas']['StreamPipeWriter'] | components['schemas']['DefaultPipeWriter'];
      /** Format: int64 */
      contentLength: number | null;
      contentType: string | null;
      cookies: components['schemas']['IResponseCookies'];
      hasStarted: boolean;
    };
    HttpValidationProblemDetails: WithRequired<
      {
        errors: {
          [key: string]: string[] | undefined;
        };
        [key: string]: unknown;
      } & components['schemas']['ProblemDetails'],
      'errors'
    >;
    IActionConstraintMetadata: Record<string, never>;
    IActionResult: Record<string, never>;
    ICustomAttributeProvider: Record<string, never>;
    IFilterMetadata: Record<string, never>;
    IFormatProvider: Record<string, never>;
    IIdentity: {
      name: string | null;
      authenticationType: string | null;
      isAuthenticated: boolean;
    };
    IOutputFormatter: Record<string, never>;
    IPAddress: {
      /** @enum {unknown} */
      addressFamily:
        | 'Unspecified'
        | 'Unix'
        | 'InterNetwork'
        | 'ImpLink'
        | 'Pup'
        | 'Chaos'
        | 'NS'
        | 'Ipx'
        | 'Iso'
        | 'Osi'
        | 'Ecma'
        | 'DataKit'
        | 'Ccitt'
        | 'Sna'
        | 'DecNet'
        | 'DataLink'
        | 'Lat'
        | 'HyperChannel'
        | 'AppleTalk'
        | 'NetBios'
        | 'VoiceView'
        | 'FireFox'
        | 'Banyan'
        | 'Atm'
        | 'InterNetworkV6'
        | 'Cluster'
        | 'Ieee12844'
        | 'Irda'
        | 'NetworkDesigners'
        | 'Max'
        | 'Packet'
        | 'ControllerAreaNetwork'
        | 'Unknown';
      /** Format: int64 */
      scopeId: number;
      isIPv6Multicast: boolean;
      isIPv6LinkLocal: boolean;
      isIPv6SiteLocal: boolean;
      isIPv6Teredo: boolean;
      isIPv6UniqueLocal: boolean;
      isIPv4MappedToIPv6: boolean;
      /**
       * Format: int64
       * @deprecated
       */
      address: number;
    };
    IPropertyFilterProvider: {
      propertyFilter: components['schemas']['ModelMetadataBooleanFunc'];
    };
    IResponseCookies: Record<string, never>;
    IResult: Record<string, never>;
    IRouter: Record<string, never>;
    IServiceProvider: Record<string, never>;
    ISession: {
      isAvailable: boolean;
      id: string;
      keys: readonly string[];
    };
    IUrlHelper: {
      actionContext:
        | components['schemas']['ActionContext']
        | components['schemas']['ActionExecutedContext']
        | components['schemas']['ActionExecutingContext']
        | components['schemas']['AuthorizationFilterContext']
        | components['schemas']['ExceptionContext']
        | components['schemas']['FilterContext']
        | components['schemas']['ResourceExecutedContext']
        | components['schemas']['ResourceExecutingContext']
        | components['schemas']['ResultExecutedContext']
        | components['schemas']['ResultExecutingContext'];
    };
    IValueProviderFactory: Record<string, never>;
    ImageAsset: WithRequired<
      {
        /** @enum {unknown} */
        siteAssetType: 'Image' | 'Font';
        assetName: string;
        /** Format: uuid */
        rootFolderNodeId: string | null;
        rootFolderNode: components['schemas']['FolderNode'] | null;
        images: readonly components['schemas']['ImageNode'][];
      } & components['schemas']['Asset'],
      'assetName' | 'images' | 'rootFolderNode' | 'rootFolderNodeId' | 'siteAssetType'
    >;
    ImageNode: WithRequired<
      {
        /** Format: int32 */
        width: number;
        /** Format: int32 */
        height: number;
        /** @enum {unknown} */
        imageSizeType: 'Original' | 'Thumbnail' | 'Mobile' | 'Tablet' | 'Desktop' | 'Hd';
      } & components['schemas']['FileNode'],
      'height' | 'imageSizeType' | 'width'
    >;
    ImageNodeDto: WithRequired<
      {
        /** @enum {unknown} */
        imageSizeType: 'Original' | 'Thumbnail' | 'Mobile' | 'Tablet' | 'Desktop' | 'Hd';
      } & components['schemas']['FileNodeDto'],
      'imageSizeType'
    >;
    /** @enum {string} */
    ImageSizeType: 'Original' | 'Thumbnail' | 'Mobile' | 'Tablet' | 'Desktop' | 'Hd';
    IndentedTextWriter: WithRequired<
      {
        encoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
        newLine: string;
        /** Format: int32 */
        indent: number;
        innerWriter:
          | components['schemas']['IndentedTextWriter']
          | components['schemas']['StreamWriter']
          | components['schemas']['StringWriter']
          | components['schemas']['NullTextWriter']
          | components['schemas']['SyncTextWriter'];
      } & components['schemas']['TextWriter'],
      'encoding' | 'indent' | 'innerWriter' | 'newLine'
    >;
    JsonResult: WithRequired<
      {
        contentType: string | null;
        serializerSettings: Record<string, unknown> | null;
        /** Format: int32 */
        statusCode: number | null;
        value: Record<string, unknown> | null;
      } & components['schemas']['ActionResult'],
      'contentType' | 'serializerSettings' | 'statusCode' | 'value'
    >;
    /** @enum {string} */
    KeyNumber: 'Exchange' | 'Signature';
    KeySizes: {
      /** Format: int32 */
      minSize: number;
      /** Format: int32 */
      maxSize: number;
      /** Format: int32 */
      skipSize: number;
    };
    Latin1Encoding: WithRequired<
      {
        isSingleByte: boolean;
      } & components['schemas']['Encoding'],
      'isSingleByte'
    >;
    Latin1EncodingSealed: components['schemas']['Latin1Encoding'];
    LocalRedirectResult: WithRequired<
      {
        permanent: boolean;
        preserveMethod: boolean;
        url: string;
        urlHelper: components['schemas']['IUrlHelper'] | null;
      } & components['schemas']['ActionResult'],
      'permanent' | 'preserveMethod' | 'url' | 'urlHelper'
    >;
    LoginResponse: WithRequired<
      {
        jwt: string;
        refreshToken: string;
      } & components['schemas']['BaseResponse'],
      'jwt' | 'refreshToken'
    >;
    ManifestResourceStream: components['schemas']['UnmanagedMemoryStream'];
    ManualResetEvent: components['schemas']['EventWaitHandle'];
    MarshalByRefObject: Record<string, never>;
    MdFieldInfo: WithRequired<
      {
        name: string | null;
        /** Format: int32 */
        metadataToken: number;
        fieldHandle: components['schemas']['RuntimeFieldHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'FieldAccessMask'
          | 'Static'
          | 'InitOnly'
          | 'Literal'
          | 'NotSerialized'
          | 'HasFieldRVA'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasFieldMarshal'
          | 'PinvokeImpl'
          | 'HasDefault'
          | 'ReservedMask';
        isSecurityCritical: boolean;
        isSecuritySafeCritical: boolean;
        isSecurityTransparent: boolean;
        fieldType: string | null;
      } & components['schemas']['RuntimeFieldInfo'],
      | 'attributes'
      | 'fieldHandle'
      | 'fieldType'
      | 'isSecurityCritical'
      | 'isSecuritySafeCritical'
      | 'isSecurityTransparent'
      | 'metadataToken'
      | 'name'
    >;
    MemberInfo: {
      /** @enum {unknown} */
      memberType:
        | 'Constructor'
        | 'Event'
        | 'Field'
        | 'Method'
        | 'Property'
        | 'TypeInfo'
        | 'Custom'
        | 'NestedType'
        | 'All';
      name: string;
      declaringType: string | null;
      reflectedType: string | null;
      module:
        | components['schemas']['RuntimeModule']
        | components['schemas']['RuntimeModuleBuilder']
        | components['schemas']['ModuleBuilder'];
      customAttributes: readonly (
        | components['schemas']['CustomAttributeData']
        | components['schemas']['RuntimeCustomAttributeData']
      )[];
      isCollectible: boolean;
      /** Format: int32 */
      metadataToken: number;
    };
    /** @enum {string} */
    MemberTypes:
      | 'Constructor'
      | 'Event'
      | 'Field'
      | 'Method'
      | 'Property'
      | 'TypeInfo'
      | 'Custom'
      | 'NestedType'
      | 'All';
    MemoryFailPoint: components['schemas']['CriticalFinalizerObject'];
    MemoryStream: WithRequired<
      {
        canRead: boolean;
        canSeek: boolean;
        canWrite: boolean;
        /** Format: int32 */
        capacity: number;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['Stream'],
      'canRead' | 'canSeek' | 'canWrite' | 'capacity' | 'length' | 'position'
    >;
    /** @enum {string} */
    MethodAttributes:
      | 'PrivateScope'
      | 'ReuseSlot'
      | 'Private'
      | 'FamANDAssem'
      | 'Assembly'
      | 'Family'
      | 'FamORAssem'
      | 'Public'
      | 'MemberAccessMask'
      | 'UnmanagedExport'
      | 'Static'
      | 'Final'
      | 'Virtual'
      | 'HideBySig'
      | 'NewSlot'
      | 'VtableLayoutMask'
      | 'CheckAccessOnOverride'
      | 'Abstract'
      | 'SpecialName'
      | 'RTSpecialName'
      | 'PinvokeImpl'
      | 'HasSecurity'
      | 'RequireSecObject'
      | 'ReservedMask';
    MethodBase: WithRequired<
      {
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        methodImplementationFlags:
          | 'IL'
          | 'Managed'
          | 'Native'
          | 'OPTIL'
          | 'CodeTypeMask'
          | 'Runtime'
          | 'ManagedMask'
          | 'Unmanaged'
          | 'NoInlining'
          | 'ForwardRef'
          | 'Synchronized'
          | 'NoOptimization'
          | 'PreserveSig'
          | 'AggressiveInlining'
          | 'AggressiveOptimization'
          | 'InternalCall'
          | 'MaxMethodImplVal';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        isAbstract: boolean;
        isConstructor: boolean;
        isFinal: boolean;
        isHideBySig: boolean;
        isSpecialName: boolean;
        isStatic: boolean;
        isVirtual: boolean;
        isAssembly: boolean;
        isFamily: boolean;
        isFamilyAndAssembly: boolean;
        isFamilyOrAssembly: boolean;
        isPrivate: boolean;
        isPublic: boolean;
        isConstructedGenericMethod: boolean;
        isGenericMethod: boolean;
        isGenericMethodDefinition: boolean;
        containsGenericParameters: boolean;
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        isSecurityCritical: boolean;
        isSecuritySafeCritical: boolean;
        isSecurityTransparent: boolean;
      } & components['schemas']['MemberInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'containsGenericParameters'
      | 'isAbstract'
      | 'isAssembly'
      | 'isConstructedGenericMethod'
      | 'isConstructor'
      | 'isFamily'
      | 'isFamilyAndAssembly'
      | 'isFamilyOrAssembly'
      | 'isFinal'
      | 'isGenericMethod'
      | 'isGenericMethodDefinition'
      | 'isHideBySig'
      | 'isPrivate'
      | 'isPublic'
      | 'isSecurityCritical'
      | 'isSecuritySafeCritical'
      | 'isSecurityTransparent'
      | 'isSpecialName'
      | 'isStatic'
      | 'isVirtual'
      | 'methodHandle'
      | 'methodImplementationFlags'
    >;
    MethodBuilder: WithRequired<
      {
        initLocals: boolean;
      } & components['schemas']['MethodInfo'],
      'initLocals'
    >;
    MethodBuilderInstantiation: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        isGenericMethodDefinition: boolean;
        containsGenericParameters: boolean;
        isGenericMethod: boolean;
        returnType: string | null;
        returnParameter: components['schemas']['ParameterInfo'] | components['schemas']['RuntimeParameterInfo'] | null;
        returnTypeCustomAttributes: components['schemas']['ICustomAttributeProvider'] | null;
      } & components['schemas']['MethodInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'containsGenericParameters'
      | 'declaringType'
      | 'isGenericMethod'
      | 'isGenericMethodDefinition'
      | 'memberType'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
      | 'returnParameter'
      | 'returnType'
      | 'returnTypeCustomAttributes'
    >;
    /** @enum {string} */
    MethodImplAttributes:
      | 'IL'
      | 'Managed'
      | 'Native'
      | 'OPTIL'
      | 'CodeTypeMask'
      | 'Runtime'
      | 'ManagedMask'
      | 'Unmanaged'
      | 'NoInlining'
      | 'ForwardRef'
      | 'Synchronized'
      | 'NoOptimization'
      | 'PreserveSig'
      | 'AggressiveInlining'
      | 'AggressiveOptimization'
      | 'InternalCall'
      | 'MaxMethodImplVal';
    MethodInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        returnParameter: components['schemas']['ParameterInfo'] | components['schemas']['RuntimeParameterInfo'];
        returnType: string;
        returnTypeCustomAttributes: components['schemas']['ICustomAttributeProvider'];
      } & components['schemas']['MethodBase'],
      'memberType' | 'returnParameter' | 'returnType' | 'returnTypeCustomAttributes'
    >;
    MethodOnTypeBuilderInstantiation: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        isGenericMethodDefinition: boolean;
        containsGenericParameters: boolean;
        isGenericMethod: boolean;
        returnType: string | null;
        returnParameter: components['schemas']['ParameterInfo'] | components['schemas']['RuntimeParameterInfo'] | null;
        returnTypeCustomAttributes: components['schemas']['ICustomAttributeProvider'] | null;
      } & components['schemas']['MethodInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'containsGenericParameters'
      | 'declaringType'
      | 'isGenericMethod'
      | 'isGenericMethodDefinition'
      | 'memberType'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
      | 'returnParameter'
      | 'returnType'
      | 'returnTypeCustomAttributes'
    >;
    ModelError: {
      exception: Record<string, unknown> | null;
      errorMessage: string;
    };
    ModelMetadataBooleanFunc: {
      target: Record<string, unknown> | null;
      method:
        | components['schemas']['RuntimeMethodInfo']
        | components['schemas']['DynamicMethod']
        | components['schemas']['RuntimeMethodBuilder']
        | components['schemas']['SymbolMethod']
        | components['schemas']['MethodBuilder']
        | components['schemas']['MethodBuilderInstantiation']
        | components['schemas']['MethodOnTypeBuilderInstantiation'];
    };
    ModelStateEntry: {
      rawValue: Record<string, unknown> | null;
      attemptedValue: string | null;
      errors: readonly components['schemas']['ModelError'][];
      /** @enum {unknown} */
      validationState: 'Unvalidated' | 'Invalid' | 'Valid' | 'Skipped';
      isContainerNode: boolean;
      children: readonly components['schemas']['ModelStateNode'][] | null;
    };
    ModelStateNode: WithRequired<
      {
        childNodes: components['schemas']['ModelStateNode'][];
        children: readonly components['schemas']['ModelStateNode'][];
        key: string;
        subKey: components['schemas']['StringSegment'];
        isContainerNode: boolean;
      } & components['schemas']['ModelStateEntry'],
      'childNodes' | 'children' | 'isContainerNode' | 'key' | 'subKey'
    >;
    /** @enum {string} */
    ModelValidationState: 'Unvalidated' | 'Invalid' | 'Valid' | 'Skipped';
    ModificationInfo: {
      /** Format: uuid */
      userId: string;
      /** Format: date-time */
      dateTime: string;
      user: components['schemas']['User'] | null;
    };
    ModificationInfoDto: {
      /** Format: uuid */
      userId: string;
      userDisplayName: string;
      /** Format: date-time */
      dateTime: string;
    };
    Module: {
      assembly:
        | components['schemas']['RuntimeAssembly']
        | components['schemas']['AssemblyBuilder']
        | components['schemas']['RuntimeAssemblyBuilder'];
      fullyQualifiedName: string;
      name: string;
      /** Format: int32 */
      mdStreamVersion: number;
      /** Format: uuid */
      moduleVersionId: string;
      scopeName: string;
      moduleHandle: components['schemas']['ModuleHandle'];
      customAttributes: readonly (
        | components['schemas']['CustomAttributeData']
        | components['schemas']['RuntimeCustomAttributeData']
      )[];
      /** Format: int32 */
      metadataToken: number;
    };
    ModuleBuilder: components['schemas']['Module'];
    ModuleHandle: {
      /** Format: int32 */
      mdStreamVersion: number;
    };
    Mutex: components['schemas']['WaitHandle'];
    NavigationItemDto: {
      id: string;
      name: string;
      page: components['schemas']['NavigationPageDto'] | null;
      children: components['schemas']['NavigationItemDto'][] | null;
    };
    NavigationPageDto: {
      /** Format: uuid */
      id: string;
      title: string;
      slug: string;
      /** @enum {unknown} */
      pageState: 'Unpublished' | 'Published';
    };
    NoContentResult: components['schemas']['StatusCodeResult'];
    Node: WithRequired<
      {
        fullName: string;
        name: string;
        parent:
          | components['schemas']['FileNode']
          | components['schemas']['FolderNode']
          | components['schemas']['ImageNode']
          | null;
        /** @enum {unknown} */
        structureType: 'DossierUpload' | 'DossierAsset' | 'SiteAsset';
        /** @enum {unknown} */
        nodeType: 'Folder' | 'File';
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'] | null;
        children: readonly (
          | components['schemas']['FileNode']
          | components['schemas']['FolderNode']
          | components['schemas']['ImageNode']
        )[];
      } & components['schemas']['BaseEntity'],
      'children' | 'created' | 'fullName' | 'modified' | 'name' | 'nodeType' | 'parent' | 'structureType'
    >;
    NodeDownloadQueryResponse: WithRequired<
      {
        /** @enum {unknown} */
        fileType: 'Folder' | 'File';
        fileName: string;
        downloadStream:
          | components['schemas']['TranscodingStream']
          | components['schemas']['ManifestResourceStream']
          | components['schemas']['BufferedStream']
          | components['schemas']['FileStream']
          | components['schemas']['MemoryStream']
          | components['schemas']['PinnedBufferMemoryStream']
          | components['schemas']['NullStream']
          | components['schemas']['SyncStream']
          | components['schemas']['UnmanagedMemoryStream']
          | components['schemas']['UnmanagedMemoryStreamWrapper']
          | components['schemas']['BufferedFileStreamStrategy']
          | components['schemas']['DerivedFileStreamStrategy']
          | components['schemas']['FileStreamStrategy']
          | components['schemas']['OSFileStreamStrategy']
          | components['schemas']['AsyncWindowsFileStreamStrategy']
          | components['schemas']['SyncWindowsFileStreamStrategy'];
      } & components['schemas']['BaseResponse'],
      'downloadStream' | 'fileName' | 'fileType'
    >;
    /** @enum {string} */
    NodeType: 'Folder' | 'File';
    NotFound: {
      /** Format: int32 */
      statusCode: number;
    };
    NotFoundObjectResult: components['schemas']['ObjectResult'];
    NotFoundResult: components['schemas']['StatusCodeResult'];
    NotUploadedFile: {
      name: string;
      fullName: string;
      /** @enum {unknown} */
      reason: 'Blacklisted' | 'FileExists' | 'DuplicateFileName' | 'NotCompressed';
    };
    /** @enum {string} */
    NotUploadedReason: 'Blacklisted' | 'FileExists' | 'DuplicateFileName' | 'NotCompressed';
    NullStream: WithRequired<
      {
        canRead: boolean;
        canWrite: boolean;
        canSeek: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['Stream'],
      'canRead' | 'canSeek' | 'canWrite' | 'length' | 'position'
    >;
    NullStreamReader: WithRequired<
      {
        currentEncoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
      } & components['schemas']['StreamReader'],
      'currentEncoding'
    >;
    NullTextWriter: WithRequired<
      {
        formatProvider: components['schemas']['IFormatProvider'];
        encoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
      } & components['schemas']['TextWriter'],
      'encoding' | 'formatProvider'
    >;
    OSFileStreamStrategy: WithRequired<
      {
        canSeek: boolean;
        canRead: boolean;
        canWrite: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['FileStreamStrategy'],
      'canRead' | 'canSeek' | 'canWrite' | 'length' | 'position'
    >;
    ObjectHandle: components['schemas']['MarshalByRefObject'];
    ObjectResult: WithRequired<
      {
        value: Record<string, unknown> | null;
        formatters: components['schemas']['IOutputFormatter'][];
        contentTypes: string[];
        declaredType: string | null;
        /** Format: int32 */
        statusCode: number | null;
      } & components['schemas']['ActionResult'],
      'contentTypes' | 'declaredType' | 'formatters' | 'statusCode' | 'value'
    >;
    Oid: {
      value: string | null;
      friendlyName: string | null;
    };
    OkObjectResult: components['schemas']['ObjectResult'];
    OkResult: components['schemas']['StatusCodeResult'];
    OpenAction: WithRequired<
      {
        tenantShortName: string;
        /** Format: uuid */
        userId: string;
        eventType: string;
        event: unknown;
        /** Format: date-time */
        eventTime: string;
      } & components['schemas']['BaseEntity'],
      'event' | 'eventTime' | 'eventType' | 'tenantShortName' | 'userId'
    >;
    Page: WithRequired<
      {
        /** @enum {unknown} */
        pageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
        label: string;
        slug: string;
        /** @enum {unknown} */
        pageState: 'Unpublished' | 'Published';
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'];
      } & components['schemas']['BaseEntity'],
      'created' | 'label' | 'modified' | 'pageState' | 'pageType' | 'slug'
    >;
    PageBlock: WithRequired<
      {
        /** Format: uuid */
        pageZoneId: string;
        /** Format: int32 */
        order: number;
        /** Format: int32 */
        colspan: number;
        /** @enum {unknown} */
        blockType: 'RichText';
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'];
        pageZone: components['schemas']['PageZone'];
      } & components['schemas']['BaseEntity'],
      'blockType' | 'colspan' | 'created' | 'modified' | 'order' | 'pageZone' | 'pageZoneId'
    >;
    PageDto: WithRequired<
      {
        pageZones: components['schemas']['PageZoneDto'][];
      } & components['schemas']['BasePageDto'],
      'pageZones'
    >;
    PageInfoDto: {
      /** Format: uuid */
      id: string;
      label: string;
      slug: string;
      /** @enum {unknown} */
      pageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
      /** @enum {unknown} */
      pageState: 'Unpublished' | 'Published';
    };
    /** @enum {string} */
    PageState: 'Unpublished' | 'Published';
    /** @enum {string} */
    PageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
    PageZone: WithRequired<
      {
        /** Format: uuid */
        pageId: string;
        /** Format: int32 */
        order: number;
        blocks: readonly components['schemas']['RichTextBlock'][];
        created: components['schemas']['ModificationInfo'];
        modified: components['schemas']['ModificationInfo'];
        homePage: components['schemas']['HomePage'];
        footerComponent: components['schemas']['FooterComponent'];
        searchPage: components['schemas']['SearchPage'];
        announcementPage: components['schemas']['AnnouncementPage'];
        simplePage: components['schemas']['SimplePage'];
      } & components['schemas']['BaseEntity'],
      | 'announcementPage'
      | 'blocks'
      | 'created'
      | 'footerComponent'
      | 'homePage'
      | 'modified'
      | 'order'
      | 'pageId'
      | 'searchPage'
      | 'simplePage'
    >;
    PageZoneDto: {
      /** Format: uuid */
      id: string;
      /** Format: int32 */
      order: number;
      blocks: components['schemas']['RichTextBlockDto'][];
    };
    PageZoneUpdateModel: {
      /** Format: uuid */
      id: string | null;
      /** Format: int32 */
      order: number;
      blocks: components['schemas']['RichTextBlockUpdateModel'][];
    };
    PagedFilterModel: {
      field: string;
      operator: string;
      value: string | null;
    };
    PagedOrderByModel: {
      field: string;
      direction: string;
    };
    /** @enum {string} */
    ParameterAttributes:
      | 'None'
      | 'In'
      | 'Out'
      | 'Lcid'
      | 'Retval'
      | 'Optional'
      | 'HasDefault'
      | 'HasFieldMarshal'
      | 'Reserved3'
      | 'Reserved4'
      | 'ReservedMask';
    ParameterDescriptor: {
      name: string;
      parameterType: string;
      bindingInfo: components['schemas']['BindingInfo'] | null;
    };
    ParameterInfo: {
      /** @enum {unknown} */
      attributes:
        | 'None'
        | 'In'
        | 'Out'
        | 'Lcid'
        | 'Retval'
        | 'Optional'
        | 'HasDefault'
        | 'HasFieldMarshal'
        | 'Reserved3'
        | 'Reserved4'
        | 'ReservedMask';
      member:
        | string
        | components['schemas']['ConstructorInfo']
        | components['schemas']['FieldInfo']
        | components['schemas']['MdFieldInfo']
        | components['schemas']['MethodBase']
        | components['schemas']['RtFieldInfo']
        | components['schemas']['RuntimeConstructorInfo']
        | components['schemas']['RuntimeEventInfo']
        | components['schemas']['RuntimeFieldInfo']
        | components['schemas']['RuntimeMethodInfo']
        | components['schemas']['RuntimePropertyInfo']
        | components['schemas']['EventInfo']
        | components['schemas']['MethodInfo']
        | components['schemas']['PropertyInfo']
        | components['schemas']['DynamicMethod']
        | components['schemas']['RuntimeConstructorBuilder']
        | components['schemas']['RuntimeFieldBuilder']
        | components['schemas']['RuntimeMethodBuilder']
        | components['schemas']['RuntimePropertyBuilder']
        | components['schemas']['SymbolMethod']
        | components['schemas']['ConstructorBuilder']
        | components['schemas']['ConstructorOnTypeBuilderInstantiation']
        | components['schemas']['FieldBuilder']
        | components['schemas']['FieldOnTypeBuilderInstantiation']
        | components['schemas']['MethodBuilder']
        | components['schemas']['MethodBuilderInstantiation']
        | components['schemas']['MethodOnTypeBuilderInstantiation']
        | components['schemas']['PropertyBuilder'];
      name: string | null;
      parameterType: string;
      /** Format: int32 */
      position: number;
      isIn: boolean;
      isLcid: boolean;
      isOptional: boolean;
      isOut: boolean;
      isRetval: boolean;
      defaultValue: Record<string, unknown> | null;
      rawDefaultValue: Record<string, unknown> | null;
      hasDefaultValue: boolean;
      customAttributes: readonly (
        | components['schemas']['CustomAttributeData']
        | components['schemas']['RuntimeCustomAttributeData']
      )[];
      /** Format: int32 */
      metadataToken: number;
    };
    Permission: WithRequired<
      {
        name: string;
      } & components['schemas']['BaseEntity'],
      'name'
    >;
    /** @enum {string} */
    PermissionType:
      | 'Dossier:Published'
      | 'Dossier:LatestVersion'
      | 'Dossier:Versions'
      | 'Dossier:GetDossierEntry'
      | 'Dossier:CreateDossier'
      | 'Dossier:UpdateDossier'
      | 'Dossier:UpdateDossierTitle'
      | 'Dossier:PublishDossier'
      | 'Dossier:UnpublishDossier'
      | 'Dossier:DeleteDossier'
      | 'Dossier:UploadZippedDossier'
      | 'Dossier:UploadAndPublishSharePointFiles'
      | 'Dossier:UploadFile'
      | 'Dossier:Paged'
      | 'Dossier:UpdateDossierImage'
      | 'Dossier:DeleteDossierImage'
      | 'DossierCategory:GetDossierCategory'
      | 'DossierCategory:GetAllDossierCategories'
      | 'DossierCategory:CreateDossierCategory'
      | 'DossierCategory:UpdateDossierCategory'
      | 'DossierCategory:DeleteDossierCategory'
      | 'DossierCategory:AddDynamicFields'
      | 'DossierCategory:GetDynamicFields'
      | 'DossierCategory:UpdateDynamicFields'
      | 'DossierCategory:DeleteDynamicFields'
      | 'DossierFileStructure:GetTopLevelNodes'
      | 'DossierFileStructure:GetNodeChildren'
      | 'DossierFileStructure:Paged'
      | 'DossierFileStructure:GetDossierMetrics'
      | 'DossierFileStructure:DeleteFileFolderNodes'
      | 'DossierFileStructure:RenameFileFolderNode'
      | 'DossierFileStructure:CreateFolder'
      | 'Download:Node'
      | 'DynamicField:GetDynamicField'
      | 'DynamicField:GetAllDynamicFields'
      | 'DynamicField:CreateDynamicField'
      | 'DynamicField:UpdateDynamicField'
      | 'DynamicField:DeleteDynamicField'
      | 'FileNode:GetDynamicFields'
      | 'FileNode:UpdateDynamicFields'
      | 'User:Me'
      | 'Admin:UserRoles:View'
      | 'Settings:UpdateSecuritySettings'
      | 'Settings:UpdateSitemapSettings'
      | 'Settings:UpdateWebStatsSettings'
      | 'Settings:UpdateSiteParameters'
      | 'Settings:UpdateHomePageContent'
      | 'Settings:UpdateNavigation'
      | 'Page:Navigation'
      | 'Page:Paged'
      | 'Page:GetPage'
      | 'Page:GetAllPages'
      | 'Page:CreatePage'
      | 'Page:UpdatePage'
      | 'Page:DeletePage'
      | 'Page:PublishPage'
      | 'Page:UnPublishPage'
      | 'ImageAsset:UpdateSiteLogo'
      | 'ImageAsset:UpdateSiteFavicon'
      | 'ImageAsset:UpdateSiteHomePageImage'
      | 'FontAsset:GetFontAsset'
      | 'FontAsset:GetAllFontAssets'
      | 'FontAsset:CreateFontAsset'
      | 'FontAsset:UpdateFontAsset'
      | 'FontAsset:UploadFonts'
      | 'FontAsset:DeleteFonts'
      | 'FontAsset:DeleteFontAsset';
    PinnedBufferMemoryStream: components['schemas']['UnmanagedMemoryStream'];
    PipeReader: Record<string, never>;
    PipeWriter: {
      canGetUnflushedBytes: boolean;
      /** Format: int64 */
      unflushedBytes: number;
    };
    Pkcs9ExtensionRequest: components['schemas']['X501Attribute'];
    ProblemDetails: {
      type: string | null;
      title: string | null;
      /** Format: int32 */
      status: number | null;
      detail: string | null;
      instance: string | null;
      [key: string]: unknown;
    };
    /** @enum {string} */
    PropertyAttributes:
      | 'None'
      | 'SpecialName'
      | 'RTSpecialName'
      | 'HasDefault'
      | 'Reserved2'
      | 'Reserved3'
      | 'Reserved4'
      | 'ReservedMask';
    PropertyBuilder: components['schemas']['PropertyInfo'];
    PropertyInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        propertyType: string;
        /** @enum {unknown} */
        attributes:
          | 'None'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasDefault'
          | 'Reserved2'
          | 'Reserved3'
          | 'Reserved4'
          | 'ReservedMask';
        isSpecialName: boolean;
        canRead: boolean;
        canWrite: boolean;
        getMethod:
          | components['schemas']['RuntimeMethodInfo']
          | components['schemas']['DynamicMethod']
          | components['schemas']['RuntimeMethodBuilder']
          | components['schemas']['SymbolMethod']
          | components['schemas']['MethodBuilder']
          | components['schemas']['MethodBuilderInstantiation']
          | components['schemas']['MethodOnTypeBuilderInstantiation']
          | null;
        setMethod:
          | components['schemas']['RuntimeMethodInfo']
          | components['schemas']['DynamicMethod']
          | components['schemas']['RuntimeMethodBuilder']
          | components['schemas']['SymbolMethod']
          | components['schemas']['MethodBuilder']
          | components['schemas']['MethodBuilderInstantiation']
          | components['schemas']['MethodOnTypeBuilderInstantiation']
          | null;
      } & components['schemas']['MemberInfo'],
      | 'attributes'
      | 'canRead'
      | 'canWrite'
      | 'getMethod'
      | 'isSpecialName'
      | 'memberType'
      | 'propertyType'
      | 'setMethod'
    >;
    PublicAssetDownloadQueryResponse: WithRequired<
      {
        downloadDetails: components['schemas']['DownloadDetails'];
      } & components['schemas']['BaseResponse'],
      'downloadDetails'
    >;
    PublicCognitiveBaseSearchResultDto: {
      highlights: {
        [key: string]: string[] | undefined;
      };
      /** Format: double */
      searchScore: number;
      id: string;
      /** Format: uuid */
      dossierId: string | null;
      dossierCategory: string;
      /** Format: uuid */
      dossierCategoryId: string | null;
      dossierVersion: string;
      dossierTitle: string;
      /** Format: date-time */
      publishedDateTime: string | null;
      /** Format: date-time */
      dossierPublishedDateTime: string | null;
      /** Format: date-time */
      publishFromDate: string | null;
      /** Format: date-time */
      publishToDate: string | null;
      /** @enum {unknown} */
      type: 'File' | 'Dossier';
    };
    PublicCognitiveDossierSearchResultDto: WithRequired<
      {
        dossierSummary: string;
        dynamicFields: {
          [key: string]: unknown;
        } | null;
      } & components['schemas']['PublicCognitiveBaseSearchResultDto'],
      'dossierSummary' | 'dynamicFields'
    >;
    PublicCognitiveFileSearchResultDto: WithRequired<
      {
        content: string;
        filepath: string;
        contentType: string;
        /** Format: uuid */
        nodeId: string;
        nodeName: string;
        /** Format: int64 */
        size: number;
        blobReference: string;
      } & components['schemas']['PublicCognitiveBaseSearchResultDto'],
      'blobReference' | 'content' | 'contentType' | 'filepath' | 'nodeId' | 'nodeName' | 'size'
    >;
    PublicDossierCategoryDto: {
      alias: string;
      label: string;
      dynamicFields: components['schemas']['PublicDynamicFieldDto'][];
    };
    PublicDossierDto: {
      /** Format: uuid */
      id: string;
      /** Format: uuid */
      dossierId: string;
      /** Format: int32 */
      version: number;
      title: string;
      /** Format: uuid */
      categoryId: string;
      summary: string | null;
      decorativeImageNode: components['schemas']['ImageNodeDto'] | null;
      created: components['schemas']['ModificationInfo'];
      modified: components['schemas']['ModificationInfo'] | null;
      publishDates: components['schemas']['PublishDateInfo'] | null;
      published: components['schemas']['ModificationInfo'] | null;
      dossierPublished: components['schemas']['ModificationInfo'] | null;
      isPublished: boolean;
      rootFolderNode: components['schemas']['PublicDossierFileStructureViewDto'] | null;
      category: components['schemas']['PublicDossierCategoryDto'];
      dynamicFieldValues: components['schemas']['PublicDossierDynamicFieldValueDto'][];
    };
    PublicDossierDynamicFieldValueDto: {
      alias: string;
      label: string;
      /** Format: uuid */
      dynamicFieldId: string;
      value: Record<string, unknown> | null;
    };
    PublicDossierFileStructureQuery: {
      /** Format: uuid */
      folderNodeId: string;
    };
    PublicDossierFileStructureViewDto: {
      /** Format: uuid */
      nodeId: string;
      nodeName: string | null;
      /** @enum {unknown} */
      nodeType: 'Folder' | 'File';
      blobReference: string | null;
      /** Format: int64 */
      size: number | null;
      /** Format: int32 */
      numberOfDescendantFiles: number | null;
      /** Format: int32 */
      numberOfDescendantFolders: number | null;
      /** Format: int64 */
      totalDescendantSize: number | null;
      children: components['schemas']['PublicDossierFileStructureViewDto'][];
    };
    PublicDynamicFieldDto: {
      /** Format: uuid */
      id: string;
      alias: string;
      label: string;
      helpText: string;
      /** @enum {unknown} */
      dynamicFieldType:
        | 'Text'
        | 'Integer'
        | 'Decimal'
        | 'Date'
        | 'Select'
        | 'RadioButton'
        | 'CheckBox'
        | 'TextArea'
        | 'StringList'
        | 'RichText'
        | 'DateTimeRange';
      defaultValue: unknown;
      options: {
        [key: string]: string | undefined;
      } | null;
      validationRules: components['schemas']['DynamicFieldValidationRuleDto'][] | null;
    };
    PublicFooterComponentResponse: {
      footerComponent: components['schemas']['FooterComponentDto'];
    };
    PublicHomePageResponse: {
      homePage: components['schemas']['HomePageDto'];
    };
    PublicImageDownloadQueryResponse: WithRequired<
      {
        downloadDetails: components['schemas']['DownloadDetails'];
      } & components['schemas']['BaseResponse'],
      'downloadDetails'
    >;
    PublicKey: {
      encodedKeyValue:
        | components['schemas']['AsnEncodedData']
        | components['schemas']['Pkcs9ExtensionRequest']
        | components['schemas']['X500DistinguishedName']
        | components['schemas']['X501Attribute']
        | components['schemas']['X509AuthorityInformationAccessExtension']
        | components['schemas']['X509AuthorityKeyIdentifierExtension']
        | components['schemas']['X509BasicConstraintsExtension']
        | components['schemas']['X509EnhancedKeyUsageExtension']
        | components['schemas']['X509Extension']
        | components['schemas']['X509KeyUsageExtension']
        | components['schemas']['X509SubjectAlternativeNameExtension']
        | components['schemas']['X509SubjectKeyIdentifierExtension'];
      encodedParameters:
        | components['schemas']['AsnEncodedData']
        | components['schemas']['Pkcs9ExtensionRequest']
        | components['schemas']['X500DistinguishedName']
        | components['schemas']['X501Attribute']
        | components['schemas']['X509AuthorityInformationAccessExtension']
        | components['schemas']['X509AuthorityKeyIdentifierExtension']
        | components['schemas']['X509BasicConstraintsExtension']
        | components['schemas']['X509EnhancedKeyUsageExtension']
        | components['schemas']['X509Extension']
        | components['schemas']['X509KeyUsageExtension']
        | components['schemas']['X509SubjectAlternativeNameExtension']
        | components['schemas']['X509SubjectKeyIdentifierExtension'];
      /** @deprecated */
      key:
        | components['schemas']['DSA']
        | components['schemas']['ECAlgorithm']
        | components['schemas']['ECDiffieHellman']
        | components['schemas']['ECDsa']
        | components['schemas']['RSA']
        | components['schemas']['DSACng']
        | components['schemas']['ECDiffieHellmanCng']
        | components['schemas']['ECDsaCng']
        | components['schemas']['RSACng']
        | components['schemas']['DSACryptoServiceProvider']
        | components['schemas']['DSAWrapper']
        | components['schemas']['ECDsaWrapper']
        | components['schemas']['ECDiffieHellmanWrapper']
        | components['schemas']['DSAOpenSsl']
        | components['schemas']['ECDiffieHellmanOpenSsl']
        | components['schemas']['ECDsaOpenSsl']
        | components['schemas']['RSAOpenSsl']
        | components['schemas']['RSABCrypt']
        | components['schemas']['RSACryptoServiceProvider'];
      oid: components['schemas']['Oid'];
    };
    PublicNavigationSettingsQueryResponse: WithRequired<
      {
        navigation: components['schemas']['NavigationItemDto'][];
        pages: components['schemas']['NavigationPageDto'][];
      } & components['schemas']['BaseResponse'],
      'navigation' | 'pages'
    >;
    PublicNodeDownloadRecord: WithRequired<
      {
        /** Format: uuid */
        nodeId: string;
        /** @enum {unknown} */
        downloadRecordType: 'AuthorizedNodeDownload' | 'PublicNodeDownload' | 'PublicZipDownload';
      } & components['schemas']['DownloadRecord'],
      'downloadRecordType' | 'nodeId'
    >;
    PublicNodesDownloadQuery: {
      nodeIds: string[];
    };
    PublicNodesDownloadResponse: WithRequired<
      {
        /** Format: uuid */
        zipId: string;
      } & components['schemas']['BaseResponse'],
      'zipId'
    >;
    PublicZip: WithRequired<
      {
        /** Format: uuid */
        dossierId: string;
        /** Format: date-time */
        createdDateTime: string;
        /** Format: date-time */
        expirationDateTime: string;
        nodeIds: readonly string[];
      } & components['schemas']['BaseEntity'],
      'createdDateTime' | 'dossierId' | 'expirationDateTime' | 'nodeIds'
    >;
    PublicZipDownloadRecord: WithRequired<
      {
        /** Format: uuid */
        zipId: string;
        /** @enum {unknown} */
        downloadRecordType: 'AuthorizedNodeDownload' | 'PublicNodeDownload' | 'PublicZipDownload';
      } & components['schemas']['DownloadRecord'],
      'downloadRecordType' | 'zipId'
    >;
    PublishAnnouncementPageCommand: {
      /** Format: uuid */
      id: string;
    };
    PublishDateInfo: {
      /** Format: date-time */
      fromDate: string;
      /** Format: date-time */
      toDate: string;
    };
    PublishDateInfoDto: {
      /** Format: date-time */
      fromDate: string | null;
      /** Format: date-time */
      toDate: string | null;
    };
    PublishDossierCommand: {
      /** Format: uuid */
      dossierId: string;
    };
    PublishDossierResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    PublishSearchPageCommand: {
      /** Format: uuid */
      id: string;
    };
    PublishSimplePageCommand: {
      /** Format: uuid */
      id: string;
    };
    QueryString: {
      value: string | null;
      hasValue: boolean;
    };
    RSA: WithRequired<
      {
        keyExchangeAlgorithm: string | null;
        signatureAlgorithm: string;
      } & components['schemas']['AsymmetricAlgorithm'],
      'keyExchangeAlgorithm' | 'signatureAlgorithm'
    >;
    RSABCrypt: WithRequired<
      {
        legalKeySizes: readonly components['schemas']['KeySizes'][] | null;
      } & components['schemas']['RSA'],
      'legalKeySizes'
    >;
    RSACng: WithRequired<
      {
        legalKeySizes: readonly components['schemas']['KeySizes'][];
        key: components['schemas']['CngKey'];
      } & components['schemas']['RSA'],
      'key' | 'legalKeySizes'
    >;
    RSACryptoServiceProvider: WithRequired<
      {
        cspKeyContainerInfo: components['schemas']['CspKeyContainerInfo'];
        /** Format: int32 */
        keySize: number;
        legalKeySizes: readonly components['schemas']['KeySizes'][];
        persistKeyInCsp: boolean;
        publicOnly: boolean;
        keyExchangeAlgorithm: string | null;
        signatureAlgorithm: string;
      } & components['schemas']['RSA'],
      | 'cspKeyContainerInfo'
      | 'keyExchangeAlgorithm'
      | 'keySize'
      | 'legalKeySizes'
      | 'persistKeyInCsp'
      | 'publicOnly'
      | 'signatureAlgorithm'
    >;
    RSAOpenSsl: components['schemas']['RSA'];
    ReadOnlyIPAddress: components['schemas']['IPAddress'];
    RedirectResult: WithRequired<
      {
        permanent: boolean;
        preserveMethod: boolean;
        url: string;
        urlHelper: components['schemas']['IUrlHelper'] | null;
      } & components['schemas']['ActionResult'],
      'permanent' | 'preserveMethod' | 'url' | 'urlHelper'
    >;
    RedirectToActionResult: WithRequired<
      {
        urlHelper: components['schemas']['IUrlHelper'] | null;
        actionName: string | null;
        controllerName: string | null;
        routeValues: {
          [key: string]: unknown;
        } | null;
        permanent: boolean;
        preserveMethod: boolean;
        fragment: string | null;
      } & components['schemas']['ActionResult'],
      'actionName' | 'controllerName' | 'fragment' | 'permanent' | 'preserveMethod' | 'routeValues' | 'urlHelper'
    >;
    RedirectToPageResult: WithRequired<
      {
        urlHelper: components['schemas']['IUrlHelper'] | null;
        pageName: string | null;
        pageHandler: string | null;
        routeValues: {
          [key: string]: unknown;
        } | null;
        permanent: boolean;
        preserveMethod: boolean;
        fragment: string | null;
        protocol: string | null;
        host: string | null;
      } & components['schemas']['ActionResult'],
      | 'fragment'
      | 'host'
      | 'pageHandler'
      | 'pageName'
      | 'permanent'
      | 'preserveMethod'
      | 'protocol'
      | 'routeValues'
      | 'urlHelper'
    >;
    RedirectToRouteResult: WithRequired<
      {
        urlHelper: components['schemas']['IUrlHelper'] | null;
        routeName: string | null;
        routeValues: {
          [key: string]: unknown;
        } | null;
        permanent: boolean;
        preserveMethod: boolean;
        fragment: string | null;
      } & components['schemas']['ActionResult'],
      'fragment' | 'permanent' | 'preserveMethod' | 'routeName' | 'routeValues' | 'urlHelper'
    >;
    RegisteredWaitHandle: components['schemas']['MarshalByRefObject'];
    RemoveDynamicFieldValidationRuleResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    RenameFileFolderNodeCommand: {
      name: string;
    };
    RequiredPropertyDynamicFieldRule: components['schemas']['DynamicFieldRule'];
    RequiredPropertyDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ResourceExecutedContext: WithRequired<
      {
        canceled: boolean;
        exception: Record<string, unknown> | null;
        exceptionDispatchInfo: components['schemas']['ExceptionDispatchInfo'] | null;
        exceptionHandled: boolean;
        result: components['schemas']['IActionResult'] | null;
      } & components['schemas']['FilterContext'],
      'canceled' | 'exception' | 'exceptionDispatchInfo' | 'exceptionHandled' | 'result'
    >;
    ResourceExecutingContext: WithRequired<
      {
        result: components['schemas']['IActionResult'] | null;
        valueProviderFactories: components['schemas']['IValueProviderFactory'][];
      } & components['schemas']['FilterContext'],
      'result' | 'valueProviderFactories'
    >;
    ResultExecutedContext: WithRequired<
      {
        canceled: boolean;
        controller: unknown;
        exception: Record<string, unknown> | null;
        exceptionDispatchInfo: components['schemas']['ExceptionDispatchInfo'] | null;
        exceptionHandled: boolean;
        result: components['schemas']['IActionResult'];
      } & components['schemas']['FilterContext'],
      'canceled' | 'controller' | 'exception' | 'exceptionDispatchInfo' | 'exceptionHandled' | 'result'
    >;
    ResultExecutingContext: WithRequired<
      {
        controller: unknown;
        result: components['schemas']['IActionResult'];
        cancel: boolean;
      } & components['schemas']['FilterContext'],
      'cancel' | 'controller' | 'result'
    >;
    RichTextBlock: components['schemas']['RichTextContentGenericPageBlock'];
    RichTextBlockContentUpdateModel: WithRequired<
      {
        content: string;
      } & components['schemas']['BlockContentUpdateModel'],
      'content'
    >;
    RichTextBlockContentUpdateModelGenericPageBlockUpdateModel: {
      blockContent: components['schemas']['RichTextBlockContentUpdateModel'];
      /** Format: uuid */
      id: string | null;
      /** Format: int32 */
      order: number;
      /** Format: int32 */
      colspan: number;
      /** @enum {unknown} */
      blockType: 'RichText';
    };
    RichTextBlockDto: components['schemas']['RichTextContentDtoGenericPageBlockDto'];
    RichTextBlockUpdateModel: components['schemas']['RichTextBlockContentUpdateModelGenericPageBlockUpdateModel'];
    RichTextContent: WithRequired<
      {
        content: string;
      } & components['schemas']['BlockContent'],
      'content'
    >;
    RichTextContentDto: WithRequired<
      {
        content: string;
      } & components['schemas']['BlockContentDto'],
      'content'
    >;
    RichTextContentDtoGenericPageBlockDto: {
      blockContent: components['schemas']['RichTextContentDto'];
      /** Format: uuid */
      id: string;
      /** Format: int32 */
      order: number;
      /** Format: int32 */
      colspan: number;
      /** @enum {unknown} */
      blockType: 'RichText';
    };
    RichTextContentGenericPageBlock: {
      blockContent: components['schemas']['RichTextContent'];
      /** Format: uuid */
      pageZoneId: string;
      /** Format: int32 */
      order: number;
      /** Format: int32 */
      colspan: number;
      /** @enum {unknown} */
      blockType: 'RichText';
      created: components['schemas']['ModificationInfo'];
      modified: components['schemas']['ModificationInfo'];
      pageZone: components['schemas']['PageZone'];
      /** Format: uuid */
      id: string;
    };
    Role: WithRequired<
      {
        name: string;
        permissions: readonly components['schemas']['Permission'][];
      } & components['schemas']['BaseEntity'],
      'name' | 'permissions'
    >;
    RoleDto: {
      name: string;
      permissions: string[] | null;
    };
    RoleWithPermissionsType: {
      name: string;
      permissions: components['schemas']['PermissionType'][];
    };
    RouteData: {
      dataTokens: {
        [key: string]: unknown;
      };
      routers: readonly components['schemas']['IRouter'][];
      values: {
        [key: string]: unknown;
      };
    };
    RtFieldInfo: WithRequired<
      {
        name: string | null;
        /** Format: int32 */
        metadataToken: number;
        fieldHandle: components['schemas']['RuntimeFieldHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'FieldAccessMask'
          | 'Static'
          | 'InitOnly'
          | 'Literal'
          | 'NotSerialized'
          | 'HasFieldRVA'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasFieldMarshal'
          | 'PinvokeImpl'
          | 'HasDefault'
          | 'ReservedMask';
        fieldType: string | null;
      } & components['schemas']['RuntimeFieldInfo'],
      'attributes' | 'fieldHandle' | 'fieldType' | 'metadataToken' | 'name'
    >;
    RuntimeAssembly: WithRequired<
      {
        /** @deprecated */
        codeBase: string | null;
        fullName: string | null;
        entryPoint:
          | components['schemas']['RuntimeMethodInfo']
          | components['schemas']['DynamicMethod']
          | components['schemas']['RuntimeMethodBuilder']
          | components['schemas']['SymbolMethod']
          | components['schemas']['MethodBuilder']
          | components['schemas']['MethodBuilderInstantiation']
          | components['schemas']['MethodOnTypeBuilderInstantiation']
          | null;
        definedTypes: readonly string[] | null;
        isCollectible: boolean;
        manifestModule:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        reflectionOnly: boolean;
        location: string | null;
        imageRuntimeVersion: string | null;
        /** @deprecated */
        globalAssemblyCache: boolean;
        /** Format: int64 */
        hostContext: number;
        isDynamic: boolean;
      } & components['schemas']['Assembly'],
      | 'codeBase'
      | 'definedTypes'
      | 'entryPoint'
      | 'fullName'
      | 'globalAssemblyCache'
      | 'hostContext'
      | 'imageRuntimeVersion'
      | 'isCollectible'
      | 'isDynamic'
      | 'location'
      | 'manifestModule'
      | 'reflectionOnly'
    >;
    RuntimeAssemblyBuilder: WithRequired<
      {
        fullName: string | null;
        manifestModule:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        reflectionOnly: boolean;
        /** Format: int64 */
        hostContext: number;
        isCollectible: boolean;
      } & components['schemas']['AssemblyBuilder'],
      'fullName' | 'hostContext' | 'isCollectible' | 'manifestModule' | 'reflectionOnly'
    >;
    RuntimeConstructorBuilder: WithRequired<
      {
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        reflectedType: string | null;
        declaringType: string | null;
        name: string | null;
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
      } & components['schemas']['ConstructorBuilder'],
      | 'attributes'
      | 'callingConvention'
      | 'declaringType'
      | 'metadataToken'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
    >;
    RuntimeConstructorInfo: WithRequired<
      {
        name: string | null;
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        declaringType: string | null;
        reflectedType: string | null;
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        isSecurityCritical: boolean;
        isSecuritySafeCritical: boolean;
        isSecurityTransparent: boolean;
        containsGenericParameters: boolean;
      } & components['schemas']['ConstructorInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'containsGenericParameters'
      | 'declaringType'
      | 'isSecurityCritical'
      | 'isSecuritySafeCritical'
      | 'isSecurityTransparent'
      | 'memberType'
      | 'metadataToken'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
    >;
    RuntimeCustomAttributeData: WithRequired<
      {
        constructor:
          | components['schemas']['RuntimeConstructorInfo']
          | components['schemas']['RuntimeConstructorBuilder']
          | components['schemas']['ConstructorBuilder']
          | components['schemas']['ConstructorOnTypeBuilderInstantiation']
          | null;
        constructorArguments: readonly components['schemas']['CustomAttributeTypedArgument'][] | null;
        namedArguments: readonly components['schemas']['CustomAttributeNamedArgument'][] | null;
      } & components['schemas']['CustomAttributeData'],
      'constructor' | 'constructorArguments' | 'namedArguments'
    >;
    RuntimeEventInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        /** @enum {unknown} */
        attributes: 'None' | 'SpecialName' | 'RTSpecialName' | 'ReservedMask';
      } & components['schemas']['EventInfo'],
      'attributes' | 'declaringType' | 'memberType' | 'metadataToken' | 'module' | 'name' | 'reflectedType'
    >;
    RuntimeFieldBuilder: WithRequired<
      {
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        fieldType: string | null;
        fieldHandle: components['schemas']['RuntimeFieldHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'FieldAccessMask'
          | 'Static'
          | 'InitOnly'
          | 'Literal'
          | 'NotSerialized'
          | 'HasFieldRVA'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasFieldMarshal'
          | 'PinvokeImpl'
          | 'HasDefault'
          | 'ReservedMask';
      } & components['schemas']['FieldBuilder'],
      | 'attributes'
      | 'declaringType'
      | 'fieldHandle'
      | 'fieldType'
      | 'metadataToken'
      | 'module'
      | 'name'
      | 'reflectedType'
    >;
    RuntimeFieldHandle: {
      value: unknown;
    };
    RuntimeFieldInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        reflectedType: string | null;
        declaringType: string | null;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        isCollectible: boolean;
      } & components['schemas']['FieldInfo'],
      'declaringType' | 'isCollectible' | 'memberType' | 'module' | 'reflectedType'
    >;
    RuntimeMethodBuilder: WithRequired<
      {
        name: string | null;
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        declaringType: string | null;
        returnTypeCustomAttributes: components['schemas']['ICustomAttributeProvider'] | null;
        reflectedType: string | null;
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        isSecurityCritical: boolean;
        isSecuritySafeCritical: boolean;
        isSecurityTransparent: boolean;
        returnType: string | null;
        returnParameter: components['schemas']['ParameterInfo'] | components['schemas']['RuntimeParameterInfo'] | null;
        isGenericMethodDefinition: boolean;
        containsGenericParameters: boolean;
        isGenericMethod: boolean;
      } & components['schemas']['MethodBuilder'],
      | 'attributes'
      | 'callingConvention'
      | 'containsGenericParameters'
      | 'declaringType'
      | 'isGenericMethod'
      | 'isGenericMethodDefinition'
      | 'isSecurityCritical'
      | 'isSecuritySafeCritical'
      | 'isSecurityTransparent'
      | 'metadataToken'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
      | 'returnParameter'
      | 'returnType'
      | 'returnTypeCustomAttributes'
    >;
    RuntimeMethodHandle: {
      value: unknown;
    };
    RuntimeMethodInfo: WithRequired<
      {
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        isSecurityCritical: boolean;
        isSecuritySafeCritical: boolean;
        isSecurityTransparent: boolean;
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        returnType: string | null;
        returnTypeCustomAttributes: components['schemas']['ICustomAttributeProvider'] | null;
        returnParameter: components['schemas']['ParameterInfo'] | components['schemas']['RuntimeParameterInfo'] | null;
        isCollectible: boolean;
        isGenericMethod: boolean;
        isGenericMethodDefinition: boolean;
        containsGenericParameters: boolean;
      } & components['schemas']['MethodInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'containsGenericParameters'
      | 'declaringType'
      | 'isCollectible'
      | 'isGenericMethod'
      | 'isGenericMethodDefinition'
      | 'isSecurityCritical'
      | 'isSecuritySafeCritical'
      | 'isSecurityTransparent'
      | 'memberType'
      | 'metadataToken'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
      | 'returnParameter'
      | 'returnType'
      | 'returnTypeCustomAttributes'
    >;
    RuntimeModule: WithRequired<
      {
        /** Format: int32 */
        mdStreamVersion: number;
        fullyQualifiedName: string | null;
        /** Format: uuid */
        moduleVersionId: string;
        /** Format: int32 */
        metadataToken: number;
        scopeName: string | null;
        name: string | null;
        assembly:
          | components['schemas']['RuntimeAssembly']
          | components['schemas']['AssemblyBuilder']
          | components['schemas']['RuntimeAssemblyBuilder']
          | null;
      } & components['schemas']['Module'],
      'assembly' | 'fullyQualifiedName' | 'mdStreamVersion' | 'metadataToken' | 'moduleVersionId' | 'name' | 'scopeName'
    >;
    RuntimeModuleBuilder: WithRequired<
      {
        fullyQualifiedName: string | null;
        /** Format: int32 */
        mdStreamVersion: number;
        /** Format: uuid */
        moduleVersionId: string;
        /** Format: int32 */
        metadataToken: number;
        scopeName: string | null;
        name: string | null;
        assembly:
          | components['schemas']['RuntimeAssembly']
          | components['schemas']['AssemblyBuilder']
          | components['schemas']['RuntimeAssemblyBuilder']
          | null;
      } & components['schemas']['ModuleBuilder'],
      'assembly' | 'fullyQualifiedName' | 'mdStreamVersion' | 'metadataToken' | 'moduleVersionId' | 'name' | 'scopeName'
    >;
    RuntimeParameterInfo: WithRequired<
      {
        parameterType: string | null;
        name: string | null;
        hasDefaultValue: boolean;
        defaultValue: Record<string, unknown> | null;
        rawDefaultValue: Record<string, unknown> | null;
        /** Format: int32 */
        metadataToken: number;
      } & components['schemas']['ParameterInfo'],
      'defaultValue' | 'hasDefaultValue' | 'metadataToken' | 'name' | 'parameterType' | 'rawDefaultValue'
    >;
    RuntimePropertyBuilder: WithRequired<
      {
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        propertyType: string | null;
        /** @enum {unknown} */
        attributes:
          | 'None'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasDefault'
          | 'Reserved2'
          | 'Reserved3'
          | 'Reserved4'
          | 'ReservedMask';
        canRead: boolean;
        canWrite: boolean;
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
      } & components['schemas']['PropertyBuilder'],
      'attributes' | 'canRead' | 'canWrite' | 'declaringType' | 'module' | 'name' | 'propertyType' | 'reflectedType'
    >;
    RuntimePropertyInfo: WithRequired<
      {
        /** @enum {unknown} */
        memberType:
          | 'Constructor'
          | 'Event'
          | 'Field'
          | 'Method'
          | 'Property'
          | 'TypeInfo'
          | 'Custom'
          | 'NestedType'
          | 'All';
        name: string | null;
        declaringType: string | null;
        reflectedType: string | null;
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        isCollectible: boolean;
        propertyType: string | null;
        /** @enum {unknown} */
        attributes:
          | 'None'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'HasDefault'
          | 'Reserved2'
          | 'Reserved3'
          | 'Reserved4'
          | 'ReservedMask';
        canRead: boolean;
        canWrite: boolean;
      } & components['schemas']['PropertyInfo'],
      | 'attributes'
      | 'canRead'
      | 'canWrite'
      | 'declaringType'
      | 'isCollectible'
      | 'memberType'
      | 'metadataToken'
      | 'module'
      | 'name'
      | 'propertyType'
      | 'reflectedType'
    >;
    SafeBuffer: WithRequired<
      {
        /** Format: int64 */
        byteLength: number;
      } & components['schemas']['SafeHandleZeroOrMinusOneIsInvalid'],
      'byteLength'
    >;
    SafeFileHandle: WithRequired<
      {
        isAsync: boolean;
      } & components['schemas']['SafeHandleZeroOrMinusOneIsInvalid'],
      'isAsync'
    >;
    SafeFindHandle: components['schemas']['SafeHandleZeroOrMinusOneIsInvalid'];
    SafeHandle: WithRequired<
      {
        isClosed: boolean;
        isInvalid: boolean;
      } & components['schemas']['CriticalFinalizerObject'],
      'isClosed' | 'isInvalid'
    >;
    SafeHandleMinusOneIsInvalid: WithRequired<
      {
        isInvalid: boolean;
      } & components['schemas']['SafeHandle'],
      'isInvalid'
    >;
    SafeHandleZeroOrMinusOneIsInvalid: WithRequired<
      {
        isInvalid: boolean;
      } & components['schemas']['SafeHandle'],
      'isInvalid'
    >;
    SafeNCryptHandle: {
      isInvalid: boolean;
      isClosed: boolean;
    };
    SafeNCryptKeyHandle: components['schemas']['SafeNCryptHandle'];
    SafeNCryptProviderHandle: components['schemas']['SafeNCryptHandle'];
    SafeNCryptSecretHandle: components['schemas']['SafeNCryptHandle'];
    SafeRegistryHandle: components['schemas']['SafeHandleZeroOrMinusOneIsInvalid'];
    SafeThreadHandle: WithRequired<
      {
        isInvalid: boolean;
      } & components['schemas']['SafeHandle'],
      'isInvalid'
    >;
    SafeThreadPoolIOHandle: WithRequired<
      {
        isInvalid: boolean;
      } & components['schemas']['SafeHandle'],
      'isInvalid'
    >;
    SafeTokenHandle: WithRequired<
      {
        isInvalid: boolean;
      } & components['schemas']['SafeHandle'],
      'isInvalid'
    >;
    SafeWaitHandle: components['schemas']['SafeHandleZeroOrMinusOneIsInvalid'];
    SearchPage: WithRequired<
      {
        /** @enum {unknown} */
        pageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
        breakOnZoneValidationError: boolean;
        categories: readonly components['schemas']['DossierCategory'][];
        pageZones: readonly components['schemas']['PageZone'][];
      } & components['schemas']['Page'],
      'breakOnZoneValidationError' | 'categories' | 'pageType' | 'pageZones'
    >;
    SearchPageDto: WithRequired<
      {
        categories: components['schemas']['DossierCategoryDto'][];
      } & components['schemas']['PageDto'],
      'categories'
    >;
    SearchPageListItemDto: components['schemas']['BasePageListItemDto'];
    /** @enum {string} */
    SearchType: 'Files' | 'Dossiers';
    /** @enum {string} */
    SecurityRuleSet: 'None' | 'Level1' | 'Level2';
    SecuritySettingsDto: {
      contact: string[];
      openPgpKey: string[] | null;
      acknowledgements: string[] | null;
      preferredLanguages: string[] | null;
      canonical: string[] | null;
      policy: string[] | null;
      hiring: string[] | null;
      csaf: string[] | null;
    };
    Semaphore: components['schemas']['WaitHandle'];
    SequencePipeReader: components['schemas']['PipeReader'];
    SignInResult: WithRequired<
      {
        authenticationScheme: string | null;
        principal: components['schemas']['ClaimsPrincipal'] | components['schemas']['GenericPrincipal'];
        properties: components['schemas']['AuthenticationProperties'] | null;
      } & components['schemas']['ActionResult'],
      'authenticationScheme' | 'principal' | 'properties'
    >;
    SignOutResult: WithRequired<
      {
        authenticationSchemes: string[];
        properties: components['schemas']['AuthenticationProperties'] | null;
      } & components['schemas']['ActionResult'],
      'authenticationSchemes' | 'properties'
    >;
    SimplePage: WithRequired<
      {
        /** @enum {unknown} */
        pageType: 'Search' | 'Announcement' | 'Simple' | 'Home' | 'Footer';
        pageZones: readonly components['schemas']['PageZone'][];
        breakOnZoneValidationError: boolean;
      } & components['schemas']['Page'],
      'breakOnZoneValidationError' | 'pageType' | 'pageZones'
    >;
    SimplePageDto: components['schemas']['PageDto'];
    SiteAssetFontDto: {
      /** @enum {unknown} */
      fontCategory: 'Heading' | 'Text' | 'Footer' | 'Undefined';
      descriptors: {
        [key: string]: string | undefined;
      };
      fonts: string[];
    };
    /** @enum {string} */
    SiteAssetType: 'Image' | 'Font';
    SiteAssetsDto: {
      logo: string | null;
      favicon: string | null;
      homePageImage: string | null;
      fonts: components['schemas']['SiteAssetFontDto'][] | null;
    };
    SiteParametersSettingsDto: {
      cssVariables: components['schemas']['StringStringKeyValuePair'][];
      tenantName: string;
    };
    SitemapSettingsDto: {
      publisher: components['schemas']['SitemapSettingsPublisherDto'];
    };
    SitemapSettingsPublisherDto: {
      id: string;
      label: string;
    };
    StandardOleMarshalObject: components['schemas']['MarshalByRefObject'];
    StatusCodeResult: WithRequired<
      {
        /** Format: int32 */
        statusCode: number;
      } & components['schemas']['ActionResult'],
      'statusCode'
    >;
    Stream: WithRequired<
      {
        canRead: boolean;
        canWrite: boolean;
        canSeek: boolean;
        canTimeout: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
        /** Format: int32 */
        readTimeout: number;
        /** Format: int32 */
        writeTimeout: number;
      } & components['schemas']['MarshalByRefObject'],
      'canRead' | 'canSeek' | 'canTimeout' | 'canWrite' | 'length' | 'position' | 'readTimeout' | 'writeTimeout'
    >;
    StreamPipeReader: WithRequired<
      {
        innerStream:
          | components['schemas']['TranscodingStream']
          | components['schemas']['ManifestResourceStream']
          | components['schemas']['BufferedStream']
          | components['schemas']['FileStream']
          | components['schemas']['MemoryStream']
          | components['schemas']['PinnedBufferMemoryStream']
          | components['schemas']['NullStream']
          | components['schemas']['SyncStream']
          | components['schemas']['UnmanagedMemoryStream']
          | components['schemas']['UnmanagedMemoryStreamWrapper']
          | components['schemas']['BufferedFileStreamStrategy']
          | components['schemas']['DerivedFileStreamStrategy']
          | components['schemas']['FileStreamStrategy']
          | components['schemas']['OSFileStreamStrategy']
          | components['schemas']['AsyncWindowsFileStreamStrategy']
          | components['schemas']['SyncWindowsFileStreamStrategy'];
      } & components['schemas']['PipeReader'],
      'innerStream'
    >;
    StreamPipeWriter: WithRequired<
      {
        innerStream:
          | components['schemas']['TranscodingStream']
          | components['schemas']['ManifestResourceStream']
          | components['schemas']['BufferedStream']
          | components['schemas']['FileStream']
          | components['schemas']['MemoryStream']
          | components['schemas']['PinnedBufferMemoryStream']
          | components['schemas']['NullStream']
          | components['schemas']['SyncStream']
          | components['schemas']['UnmanagedMemoryStream']
          | components['schemas']['UnmanagedMemoryStreamWrapper']
          | components['schemas']['BufferedFileStreamStrategy']
          | components['schemas']['DerivedFileStreamStrategy']
          | components['schemas']['FileStreamStrategy']
          | components['schemas']['OSFileStreamStrategy']
          | components['schemas']['AsyncWindowsFileStreamStrategy']
          | components['schemas']['SyncWindowsFileStreamStrategy'];
        canGetUnflushedBytes: boolean;
        /** Format: int64 */
        unflushedBytes: number;
      } & components['schemas']['PipeWriter'],
      'canGetUnflushedBytes' | 'innerStream' | 'unflushedBytes'
    >;
    StreamReader: WithRequired<
      {
        currentEncoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
        baseStream:
          | components['schemas']['TranscodingStream']
          | components['schemas']['ManifestResourceStream']
          | components['schemas']['BufferedStream']
          | components['schemas']['FileStream']
          | components['schemas']['MemoryStream']
          | components['schemas']['PinnedBufferMemoryStream']
          | components['schemas']['NullStream']
          | components['schemas']['SyncStream']
          | components['schemas']['UnmanagedMemoryStream']
          | components['schemas']['UnmanagedMemoryStreamWrapper']
          | components['schemas']['BufferedFileStreamStrategy']
          | components['schemas']['DerivedFileStreamStrategy']
          | components['schemas']['FileStreamStrategy']
          | components['schemas']['OSFileStreamStrategy']
          | components['schemas']['AsyncWindowsFileStreamStrategy']
          | components['schemas']['SyncWindowsFileStreamStrategy'];
        endOfStream: boolean;
      } & components['schemas']['TextReader'],
      'baseStream' | 'currentEncoding' | 'endOfStream'
    >;
    StreamWriter: WithRequired<
      {
        autoFlush: boolean;
        baseStream:
          | components['schemas']['TranscodingStream']
          | components['schemas']['ManifestResourceStream']
          | components['schemas']['BufferedStream']
          | components['schemas']['FileStream']
          | components['schemas']['MemoryStream']
          | components['schemas']['PinnedBufferMemoryStream']
          | components['schemas']['NullStream']
          | components['schemas']['SyncStream']
          | components['schemas']['UnmanagedMemoryStream']
          | components['schemas']['UnmanagedMemoryStreamWrapper']
          | components['schemas']['BufferedFileStreamStrategy']
          | components['schemas']['DerivedFileStreamStrategy']
          | components['schemas']['FileStreamStrategy']
          | components['schemas']['OSFileStreamStrategy']
          | components['schemas']['AsyncWindowsFileStreamStrategy']
          | components['schemas']['SyncWindowsFileStreamStrategy'];
        encoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
      } & components['schemas']['TextWriter'],
      'autoFlush' | 'baseStream' | 'encoding'
    >;
    StringReader: components['schemas']['TextReader'];
    StringSegment: {
      buffer: string | null;
      /** Format: int32 */
      offset: number;
      /** Format: int32 */
      length: number;
      value: string | null;
      hasValue: boolean;
    };
    StringStringKeyValuePair: {
      key: string;
      value: string;
    };
    StringStringValuesKeyValuePair: {
      key: string;
      value: string[];
    };
    StringWriter: WithRequired<
      {
        encoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
      } & components['schemas']['TextWriter'],
      'encoding'
    >;
    /** @enum {string} */
    StructureType: 'DossierUpload' | 'DossierAsset' | 'SiteAsset';
    SymbolMethod: WithRequired<
      {
        /** Format: int32 */
        metadataToken: number;
        module:
          | components['schemas']['RuntimeModule']
          | components['schemas']['RuntimeModuleBuilder']
          | components['schemas']['ModuleBuilder']
          | null;
        reflectedType: string | null;
        name: string | null;
        declaringType: string | null;
        /** @enum {unknown} */
        attributes:
          | 'PrivateScope'
          | 'ReuseSlot'
          | 'Private'
          | 'FamANDAssem'
          | 'Assembly'
          | 'Family'
          | 'FamORAssem'
          | 'Public'
          | 'MemberAccessMask'
          | 'UnmanagedExport'
          | 'Static'
          | 'Final'
          | 'Virtual'
          | 'HideBySig'
          | 'NewSlot'
          | 'VtableLayoutMask'
          | 'CheckAccessOnOverride'
          | 'Abstract'
          | 'SpecialName'
          | 'RTSpecialName'
          | 'PinvokeImpl'
          | 'HasSecurity'
          | 'RequireSecObject'
          | 'ReservedMask';
        /** @enum {unknown} */
        callingConvention: 'Standard' | 'VarArgs' | 'Any' | 'HasThis' | 'ExplicitThis';
        methodHandle: components['schemas']['RuntimeMethodHandle'];
        returnType: string | null;
        returnTypeCustomAttributes: components['schemas']['ICustomAttributeProvider'] | null;
      } & components['schemas']['MethodInfo'],
      | 'attributes'
      | 'callingConvention'
      | 'declaringType'
      | 'metadataToken'
      | 'methodHandle'
      | 'module'
      | 'name'
      | 'reflectedType'
      | 'returnType'
      | 'returnTypeCustomAttributes'
    >;
    SyncStream: WithRequired<
      {
        canRead: boolean;
        canWrite: boolean;
        canSeek: boolean;
        canTimeout: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
        /** Format: int32 */
        readTimeout: number;
        /** Format: int32 */
        writeTimeout: number;
      } & components['schemas']['Stream'],
      'canRead' | 'canSeek' | 'canTimeout' | 'canWrite' | 'length' | 'position' | 'readTimeout' | 'writeTimeout'
    >;
    SyncTextReader: components['schemas']['TextReader'];
    SyncTextWriter: WithRequired<
      {
        encoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
        formatProvider: components['schemas']['IFormatProvider'];
        newLine: string;
      } & components['schemas']['TextWriter'],
      'encoding' | 'formatProvider' | 'newLine'
    >;
    SyncWindowsFileStreamStrategy: components['schemas']['OSFileStreamStrategy'];
    TextReader: components['schemas']['MarshalByRefObject'];
    TextWriter: WithRequired<
      {
        formatProvider: components['schemas']['IFormatProvider'];
        encoding:
          | components['schemas']['ASCIIEncoding']
          | components['schemas']['ASCIIEncodingSealed']
          | components['schemas']['Latin1Encoding']
          | components['schemas']['Latin1EncodingSealed']
          | components['schemas']['UnicodeEncoding']
          | components['schemas']['UTF32Encoding']
          | components['schemas']['UTF7Encoding']
          | components['schemas']['UTF8Encoding']
          | components['schemas']['UTF8EncodingSealed'];
        newLine: string;
      } & components['schemas']['MarshalByRefObject'],
      'encoding' | 'formatProvider' | 'newLine'
    >;
    Thread: WithRequired<
      {
        /** Format: int32 */
        managedThreadId: number;
        isAlive: boolean;
        isBackground: boolean;
        isThreadPoolThread: boolean;
        /** @enum {unknown} */
        priority: 'Lowest' | 'BelowNormal' | 'Normal' | 'AboveNormal' | 'Highest';
        /** @enum {unknown} */
        threadState:
          | 'Running'
          | 'StopRequested'
          | 'SuspendRequested'
          | 'Background'
          | 'Unstarted'
          | 'Stopped'
          | 'WaitSleepJoin'
          | 'Suspended'
          | 'AbortRequested'
          | 'Aborted';
        currentCulture: string;
        currentUICulture: string;
        executionContext: components['schemas']['ExecutionContext'] | null;
        name: string | null;
        /**
         * @deprecated
         * @enum {unknown}
         */
        apartmentState: 'STA' | 'MTA' | 'Unknown';
      } & components['schemas']['CriticalFinalizerObject'],
      | 'apartmentState'
      | 'currentCulture'
      | 'currentUICulture'
      | 'executionContext'
      | 'isAlive'
      | 'isBackground'
      | 'isThreadPoolThread'
      | 'managedThreadId'
      | 'name'
      | 'priority'
      | 'threadState'
    >;
    /** @enum {string} */
    ThreadPriority: 'Lowest' | 'BelowNormal' | 'Normal' | 'AboveNormal' | 'Highest';
    /** @enum {string} */
    ThreadState:
      | 'Running'
      | 'StopRequested'
      | 'SuspendRequested'
      | 'Background'
      | 'Unstarted'
      | 'Stopped'
      | 'WaitSleepJoin'
      | 'Suspended'
      | 'AbortRequested'
      | 'Aborted';
    Timer: components['schemas']['MarshalByRefObject'];
    TranscodingStream: WithRequired<
      {
        canRead: boolean;
        canSeek: boolean;
        canWrite: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['Stream'],
      'canRead' | 'canSeek' | 'canWrite' | 'length' | 'position'
    >;
    TypeObjectKeyValuePair: {
      key: string;
      value: unknown;
    };
    UTF32Encoding: components['schemas']['Encoding'];
    UTF7Encoding: components['schemas']['Encoding'];
    UTF8Encoding: components['schemas']['Encoding'];
    UTF8EncodingSealed: components['schemas']['UTF8Encoding'];
    UnPublishAnnouncementPageCommand: {
      /** Format: uuid */
      id: string;
    };
    UnPublishSearchPageCommand: {
      /** Format: uuid */
      id: string;
    };
    UnPublishSimplePageCommand: {
      /** Format: uuid */
      id: string;
    };
    UnauthorizedObjectResult: components['schemas']['ObjectResult'];
    UnauthorizedResult: components['schemas']['StatusCodeResult'];
    UnicodeEncoding: components['schemas']['Encoding'];
    /** @enum {string} */
    UnixFileMode:
      | 'None'
      | 'OtherExecute'
      | 'OtherWrite'
      | 'OtherRead'
      | 'GroupExecute'
      | 'GroupWrite'
      | 'GroupRead'
      | 'UserExecute'
      | 'UserWrite'
      | 'UserRead'
      | 'StickyBit'
      | 'SetGroup'
      | 'SetUser';
    UnmanagedBuffer: components['schemas']['SafeBuffer'];
    UnmanagedMemoryStream: WithRequired<
      {
        canRead: boolean;
        canSeek: boolean;
        canWrite: boolean;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        capacity: number;
        /** Format: int64 */
        position: number;
        positionPointer: components['schemas']['Byte*'] | null;
      } & components['schemas']['Stream'],
      'canRead' | 'canSeek' | 'canWrite' | 'capacity' | 'length' | 'position' | 'positionPointer'
    >;
    UnmanagedMemoryStreamWrapper: WithRequired<
      {
        canRead: boolean;
        canSeek: boolean;
        canWrite: boolean;
        /** Format: int32 */
        capacity: number;
        /** Format: int64 */
        length: number;
        /** Format: int64 */
        position: number;
      } & components['schemas']['MemoryStream'],
      'canRead' | 'canSeek' | 'canWrite' | 'capacity' | 'length' | 'position'
    >;
    UnprocessableEntityObjectResult: components['schemas']['ObjectResult'];
    UnprocessableEntityResult: components['schemas']['StatusCodeResult'];
    UnpublishDossierCommand: {
      /** Format: uuid */
      dossierId: string;
    };
    UnpublishDossierResponse: WithRequired<
      {
        /** Format: uuid */
        dossierId: string;
        /** Format: int32 */
        newVersion: number;
      } & components['schemas']['BaseResponse'],
      'dossierId' | 'newVersion'
    >;
    UnsupportedMediaTypeResult: components['schemas']['StatusCodeResult'];
    UpdateAnnouncementPageCommand: WithRequired<
      {
        organisationName: string;
        organisationType: string;
        publicationType: string;
        zones: components['schemas']['PageZoneUpdateModel'][];
        /** Format: uuid */
        id: string | null;
      } & components['schemas']['CreateAnnouncementPageCommand'],
      'id' | 'organisationName' | 'organisationType' | 'publicationType' | 'zones'
    >;
    UpdateDossierCategoryCommand: {
      /** Format: uuid */
      dossierCategoryId: string;
      alias: string;
      label: string;
      requirePublishDates: boolean;
    };
    UpdateDossierCategoryDynamicFieldsResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    UpdateDossierCategoryResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    UpdateDossierCommand: {
      /** Format: uuid */
      dossierId: string;
      summary: string;
      dynamicFieldValues: {
        [key: string]: unknown;
      };
      /** Format: date-time */
      publishFromDate: string | null;
      /** Format: date-time */
      publishToDate: string | null;
    };
    UpdateDossierDynamicFieldsCommand: {
      /** Format: uuid */
      categoryId: string | null;
    };
    UpdateDossierDynamicFieldsResponse: WithRequired<
      {
        /** Format: int32 */
        totalProcessed: number;
        updatedDrafts: components['schemas']['UpdateResult'];
        published: components['schemas']['UpdateResult'];
      } & components['schemas']['BaseResponse'],
      'published' | 'totalProcessed' | 'updatedDrafts'
    >;
    UpdateDossierImageCommand: {
      /** Format: uuid */
      dossierId: string;
      fileName: string;
      stream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy']
        | null;
    };
    UpdateDossierResponse: WithRequired<
      {
        dossier: components['schemas']['DossierDto'];
      } & components['schemas']['BaseResponse'],
      'dossier'
    >;
    UpdateDossierTitleCommand: {
      /** Format: uuid */
      dossierId: string;
      title: string;
    };
    UpdateDossierTitleResponse: WithRequired<
      {
        dossier: components['schemas']['DossierDto'];
      } & components['schemas']['BaseResponse'],
      'dossier'
    >;
    UpdateDynamicFieldCommand: {
      /** Format: uuid */
      dynamicFieldId: string;
      alias: string;
      label: string;
      helpText: string;
      /** @enum {unknown} */
      dynamicFieldType:
        | 'Text'
        | 'Integer'
        | 'Decimal'
        | 'Date'
        | 'Select'
        | 'RadioButton'
        | 'CheckBox'
        | 'TextArea'
        | 'StringList'
        | 'RichText'
        | 'DateTimeRange';
      defaultValue: Record<string, unknown> | null;
      options: {
        [key: string]: string | undefined;
      } | null;
    };
    UpdateDynamicFieldResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    UpdateDynamicFieldValidationRuleCommand: {
      /** Format: uuid */
      dynamicFieldId: string;
      validationRuleType: components['schemas']['DynamicFieldValidationRuleType'];
      fieldRuleValue:
        | components['schemas']['RequiredPropertyDynamicFieldRule']
        | components['schemas']['ValidateDateDynamicFieldRule']
        | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
        | components['schemas']['ValidateFutureDateDynamicFieldRule']
        | components['schemas']['ValidateGuidDynamicFieldRule']
        | components['schemas']['ValidateIpAddressDynamicFieldRule']
        | components['schemas']['ValidateNumberRangeDynamicFieldRule']
        | components['schemas']['ValidatePastDateDynamicFieldRule']
        | components['schemas']['ValidateStringLengthDynamicFieldRule'];
    };
    UpdateDynamicFieldValidationRuleResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    UpdateDynamicFieldValidationRulesCommand: {
      /** Format: uuid */
      dynamicFieldId: string;
      validationRules: {
        ValidateGuid: components['schemas']['ValidationRuleEventObject'];
        RequiredProperty: components['schemas']['ValidationRuleEventObject'];
        ValidateNumberRange: components['schemas']['ValidationRuleEventObject'];
        ValidateStringLength: components['schemas']['ValidationRuleEventObject'];
        ValidateDate: components['schemas']['ValidationRuleEventObject'];
        ValidatePastDate: components['schemas']['ValidationRuleEventObject'];
        ValidateFutureDate: components['schemas']['ValidationRuleEventObject'];
        ValidateIpAddress: components['schemas']['ValidationRuleEventObject'];
        ValidateDateTimeRange: components['schemas']['ValidationRuleEventObject'];
      };
    };
    UpdateDynamicFieldValidationRulesResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    UpdateFileNodeDynamicFieldsResponse: WithRequired<
      {
        success: boolean;
      } & components['schemas']['BaseResponse'],
      'success'
    >;
    UpdateFontAssetCommand: {
      /** Format: uuid */
      fontAssetId: string;
      fontFamily: string;
      /** @enum {unknown} */
      fontCategory: 'Heading' | 'Text' | 'Footer' | 'Undefined';
      /** @enum {unknown} */
      fontWeight: 'W100' | 'W200' | 'W300' | 'W400' | 'W500' | 'W600' | 'W700' | 'W800' | 'W900' | 'Undefined';
      /** @enum {unknown} */
      fontStyle: 'Normal' | 'Italic' | 'Oblique' | 'Initial' | 'Inherit' | 'Undefined';
      descriptors: {
        [key: string]: string | undefined;
      };
    };
    UpdateFontAssetResponse: {
      fontAsset: components['schemas']['FontAssetDto'];
    };
    UpdateFooterComponentCommand: {
      zones: components['schemas']['PageZoneUpdateModel'][];
    };
    UpdateFooterComponentResponse: WithRequired<
      {
        footerComponent: components['schemas']['FooterComponentDto'];
      } & components['schemas']['BaseResponse'],
      'footerComponent'
    >;
    UpdateHomePageCommand: {
      zones: components['schemas']['PageZoneUpdateModel'][];
    };
    UpdateHomePageResponse: WithRequired<
      {
        homePage: components['schemas']['HomePageDto'];
      } & components['schemas']['BaseResponse'],
      'homePage'
    >;
    UpdateHomepageContentSettingsCommand: {
      main: string;
      bottomLeft: string;
      bottomRight: string;
      footerLeft: string;
      footerRight: string;
    };
    UpdateImageAssetFaviconCommand: {
      fileName: string;
      stream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy'];
    };
    UpdateImageAssetHomePageCommand: {
      fileName: string;
      stream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy'];
    };
    UpdateImageAssetLogoCommand: {
      fileName: string;
      stream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy'];
    };
    UpdateNavigationItemCommandModel: {
      id: string;
      name: string;
      page: components['schemas']['UpdateNavigationPageCommandModel'] | null;
      children: components['schemas']['UpdateNavigationItemCommandModel'][] | null;
    };
    UpdateNavigationPageCommandModel: {
      /** Format: uuid */
      id: string;
    };
    UpdateNavigationSettingsCommand: {
      navigation: components['schemas']['UpdateNavigationItemCommandModel'][];
    };
    UpdatePageResponse: WithRequired<
      {
        page:
          | components['schemas']['AnnouncementPageDto']
          | components['schemas']['FooterComponentDto']
          | components['schemas']['HomePageDto']
          | components['schemas']['SearchPageDto']
          | components['schemas']['SimplePageDto'];
      } & components['schemas']['BaseResponse'],
      'page'
    >;
    UpdateResult: {
      /** Format: int32 */
      count: number;
      dossiers: components['schemas']['DossierUpdateDetail'][];
    };
    UpdateSearchPageCommand: WithRequired<
      {
        zones: components['schemas']['PageZoneUpdateModel'][];
        categoryIds: string[];
        /** Format: uuid */
        id: string | null;
      } & components['schemas']['CreateSearchPageCommand'],
      'categoryIds' | 'id' | 'zones'
    >;
    UpdateSecuritySettingsCommand: {
      contact: string[];
      openPgpKey: string[] | null;
      acknowledgements: string[] | null;
      preferredLanguages: string[] | null;
      canonical: string[] | null;
      policy: string[] | null;
      hiring: string[] | null;
      csaf: string[] | null;
    };
    UpdateSimplePageCommand: WithRequired<
      {
        zones: components['schemas']['PageZoneUpdateModel'][];
        /** Format: uuid */
        id: string | null;
      } & components['schemas']['CreateSimplePageCommand'],
      'id' | 'zones'
    >;
    UpdateSiteParametersSettingsCommand: {
      cssVariables: components['schemas']['StringStringKeyValuePair'][];
      tenantName: string;
    };
    UpdateSitemapSettingsCommand: {
      publisher: components['schemas']['UpdateSitemapSettingsItemCommandModel'];
    };
    UpdateSitemapSettingsItemCommandModel: {
      id: string;
      label: string;
    };
    UpdateWebStatsSettingsCommand: {
      type: string;
      key: string;
      domain: string;
    };
    UploadAndPublishDossierSharePointFilesCommand: {
      /** Format: uuid */
      siteId: string;
      /** Format: uuid */
      listId: string;
      selectedFiles: components['schemas']['DownloadSharePointFileDetails'][];
    };
    UploadAndPublishSharepointFilesResponse: WithRequired<
      {
        notUploadedFiles: components['schemas']['NotUploadedFile'][];
      } & components['schemas']['BaseResponse'],
      'notUploadedFiles'
    >;
    UploadDossierSingleFileCommand: {
      /** Format: uuid */
      folderId: string | null;
      fileName: string;
      stream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy'];
      /** Format: uuid */
      dossierId: string;
    };
    UploadDossierZipCommand: {
      fileName: string;
      zipStream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy']
        | null;
      /** Format: uuid */
      dossierId: string;
    };
    UploadDossierZipResponse: WithRequired<
      {
        dossier: components['schemas']['DossierDto'];
        notUploadedFiles: components['schemas']['NotUploadedFile'][];
      } & components['schemas']['BaseResponse'],
      'dossier' | 'notUploadedFiles'
    >;
    UploadFontCommand: {
      /** Format: uuid */
      fontAssetId: string;
      fileName: string;
      stream:
        | components['schemas']['TranscodingStream']
        | components['schemas']['ManifestResourceStream']
        | components['schemas']['BufferedStream']
        | components['schemas']['FileStream']
        | components['schemas']['MemoryStream']
        | components['schemas']['PinnedBufferMemoryStream']
        | components['schemas']['NullStream']
        | components['schemas']['SyncStream']
        | components['schemas']['UnmanagedMemoryStream']
        | components['schemas']['UnmanagedMemoryStreamWrapper']
        | components['schemas']['BufferedFileStreamStrategy']
        | components['schemas']['DerivedFileStreamStrategy']
        | components['schemas']['FileStreamStrategy']
        | components['schemas']['OSFileStreamStrategy']
        | components['schemas']['AsyncWindowsFileStreamStrategy']
        | components['schemas']['SyncWindowsFileStreamStrategy'];
    };
    User: WithRequired<
      {
        username: string;
        displayName: string;
        firstName: string;
        lastName: string;
        email: string;
        userMappings: readonly components['schemas']['UserMapping'][];
        /** Format: date-time */
        createdDateTime: string;
        /** Format: date-time */
        modifiedDateTime: string;
      } & components['schemas']['BaseEntity'],
      | 'createdDateTime'
      | 'displayName'
      | 'email'
      | 'firstName'
      | 'lastName'
      | 'modifiedDateTime'
      | 'userMappings'
      | 'username'
    >;
    UserCommand: unknown;
    UserDto: {
      /** Format: uuid */
      id: string;
      username: string;
      displayName: string;
      firstName: string;
      lastName: string;
      email: string;
      userRoles: components['schemas']['RoleDto'][] | null;
    };
    UserMapping: WithRequired<
      {
        /** Format: uuid */
        userId: string;
        externalUserId: string;
        /** Format: date-time */
        createdDateTime: string;
        /** Format: date-time */
        modifiedDateTime: string;
        user: components['schemas']['User'];
      } & components['schemas']['BaseEntity'],
      'createdDateTime' | 'externalUserId' | 'modifiedDateTime' | 'user' | 'userId'
    >;
    ValidateDateDynamicFieldRule: components['schemas']['DynamicFieldRule'];
    ValidateDateDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidateDateTimeRangeDynamicFieldRule: components['schemas']['DynamicFieldRule'];
    ValidateDateTimeRangeDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidateFutureDateDynamicFieldRule: components['schemas']['DynamicFieldRule'];
    ValidateFutureDateDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidateGuidDynamicFieldRule: components['schemas']['DynamicFieldRule'];
    ValidateGuidDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidateIpAddressDynamicFieldRule: components['schemas']['DynamicFieldRule'];
    ValidateIpAddressDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidateNumberRangeDynamicFieldRule: WithRequired<
      {
        /** Format: double */
        minimumValue: number;
        /** Format: double */
        maximumValue: number;
      } & components['schemas']['DynamicFieldRule'],
      'maximumValue' | 'minimumValue'
    >;
    ValidateNumberRangeDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidatePastDateDynamicFieldRule: components['schemas']['DynamicFieldRule'];
    ValidatePastDateDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidateStringLengthDynamicFieldRule: WithRequired<
      {
        /** Format: int32 */
        minimumLength: number;
        /** Format: int32 */
        maximumLength: number;
      } & components['schemas']['DynamicFieldRule'],
      'maximumLength' | 'minimumLength'
    >;
    ValidateStringLengthDynamicValidationRule: WithRequired<
      {
        ruleValue: string;
        fieldRuleValue:
          | components['schemas']['RequiredPropertyDynamicFieldRule']
          | components['schemas']['ValidateDateDynamicFieldRule']
          | components['schemas']['ValidateDateTimeRangeDynamicFieldRule']
          | components['schemas']['ValidateFutureDateDynamicFieldRule']
          | components['schemas']['ValidateGuidDynamicFieldRule']
          | components['schemas']['ValidateIpAddressDynamicFieldRule']
          | components['schemas']['ValidateNumberRangeDynamicFieldRule']
          | components['schemas']['ValidatePastDateDynamicFieldRule']
          | components['schemas']['ValidateStringLengthDynamicFieldRule'];
      } & components['schemas']['DynamicFieldValidationRule'],
      'fieldRuleValue' | 'ruleValue'
    >;
    ValidationRuleEventObject: {
      /** Format: double */
      minimumValue: number;
      /** Format: double */
      maximumValue: number;
      /** Format: int32 */
      minimumLength: number;
      /** Format: int32 */
      maximumLength: number;
      /** Format: date-time */
      minimumDateTime: string;
      /** Format: date-time */
      maximumDateTime: string;
    };
    WaitHandle: WithRequired<
      {
        /** @deprecated */
        handle: unknown;
        safeWaitHandle: components['schemas']['SafeWaitHandle'];
      } & components['schemas']['MarshalByRefObject'],
      'handle' | 'safeWaitHandle'
    >;
    WebSocketManager: {
      isWebSocketRequest: boolean;
      webSocketRequestedProtocols: readonly string[];
    };
    WebStatsSettingsDto: {
      type: string;
      key: string;
      domain: string;
    };
    WooCategoryDto: {
      /** Format: uuid */
      id: string;
      alias: string;
      label: string;
      /** Format: int32 */
      wooCategoryId: number;
      /** Format: int64 */
      count: number;
    };
    X500DistinguishedName: WithRequired<
      {
        name: string;
      } & components['schemas']['AsnEncodedData'],
      'name'
    >;
    X501Attribute: components['schemas']['AsnEncodedData'];
    X509AuthorityInformationAccessExtension: components['schemas']['X509Extension'];
    X509AuthorityKeyIdentifierExtension: WithRequired<
      {
        keyIdentifier: components['schemas']['ByteReadOnlyMemory'] | null;
        namedIssuer: components['schemas']['X500DistinguishedName'] | null;
        rawIssuer: components['schemas']['ByteReadOnlyMemory'] | null;
        serialNumber: components['schemas']['ByteReadOnlyMemory'] | null;
      } & components['schemas']['X509Extension'],
      'keyIdentifier' | 'namedIssuer' | 'rawIssuer' | 'serialNumber'
    >;
    X509BasicConstraintsExtension: WithRequired<
      {
        certificateAuthority: boolean;
        hasPathLengthConstraint: boolean;
        /** Format: int32 */
        pathLengthConstraint: number;
      } & components['schemas']['X509Extension'],
      'certificateAuthority' | 'hasPathLengthConstraint' | 'pathLengthConstraint'
    >;
    X509Certificate: {
      handle: unknown;
      issuer: string;
      subject: string;
      serialNumberBytes: components['schemas']['ByteReadOnlyMemory'];
    };
    X509Certificate2: WithRequired<
      {
        archived: boolean;
        extensions: readonly (
          | components['schemas']['X509Extension']
          | components['schemas']['X509AuthorityInformationAccessExtension']
          | components['schemas']['X509AuthorityKeyIdentifierExtension']
          | components['schemas']['X509BasicConstraintsExtension']
          | components['schemas']['X509EnhancedKeyUsageExtension']
          | components['schemas']['X509KeyUsageExtension']
          | components['schemas']['X509SubjectAlternativeNameExtension']
          | components['schemas']['X509SubjectKeyIdentifierExtension']
        )[];
        friendlyName: string;
        hasPrivateKey: boolean;
        /** @deprecated */
        privateKey:
          | components['schemas']['DSA']
          | components['schemas']['ECAlgorithm']
          | components['schemas']['ECDiffieHellman']
          | components['schemas']['ECDsa']
          | components['schemas']['RSA']
          | components['schemas']['DSACng']
          | components['schemas']['ECDiffieHellmanCng']
          | components['schemas']['ECDsaCng']
          | components['schemas']['RSACng']
          | components['schemas']['DSACryptoServiceProvider']
          | components['schemas']['DSAWrapper']
          | components['schemas']['ECDsaWrapper']
          | components['schemas']['ECDiffieHellmanWrapper']
          | components['schemas']['DSAOpenSsl']
          | components['schemas']['ECDiffieHellmanOpenSsl']
          | components['schemas']['ECDsaOpenSsl']
          | components['schemas']['RSAOpenSsl']
          | components['schemas']['RSABCrypt']
          | components['schemas']['RSACryptoServiceProvider']
          | null;
        issuerName: components['schemas']['X500DistinguishedName'];
        /** Format: date-time */
        notAfter: string;
        /** Format: date-time */
        notBefore: string;
        publicKey: components['schemas']['PublicKey'];
        /** Format: byte */
        rawData: string;
        rawDataMemory: components['schemas']['ByteReadOnlyMemory'];
        serialNumber: string;
        signatureAlgorithm: components['schemas']['Oid'];
        subjectName: components['schemas']['X500DistinguishedName'];
        thumbprint: string;
        /** Format: int32 */
        version: number;
      } & components['schemas']['X509Certificate'],
      | 'archived'
      | 'extensions'
      | 'friendlyName'
      | 'hasPrivateKey'
      | 'issuerName'
      | 'notAfter'
      | 'notBefore'
      | 'privateKey'
      | 'publicKey'
      | 'rawData'
      | 'rawDataMemory'
      | 'serialNumber'
      | 'signatureAlgorithm'
      | 'subjectName'
      | 'thumbprint'
      | 'version'
    >;
    X509EnhancedKeyUsageExtension: WithRequired<
      {
        enhancedKeyUsages: unknown[];
      } & components['schemas']['X509Extension'],
      'enhancedKeyUsages'
    >;
    X509Extension: WithRequired<
      {
        critical: boolean;
      } & components['schemas']['AsnEncodedData'],
      'critical'
    >;
    X509KeyUsageExtension: WithRequired<
      {
        /** @enum {unknown} */
        keyUsages:
          | 'None'
          | 'EncipherOnly'
          | 'CrlSign'
          | 'KeyCertSign'
          | 'KeyAgreement'
          | 'DataEncipherment'
          | 'KeyEncipherment'
          | 'NonRepudiation'
          | 'DigitalSignature'
          | 'DecipherOnly';
      } & components['schemas']['X509Extension'],
      'keyUsages'
    >;
    /** @enum {string} */
    X509KeyUsageFlags:
      | 'None'
      | 'EncipherOnly'
      | 'CrlSign'
      | 'KeyCertSign'
      | 'KeyAgreement'
      | 'DataEncipherment'
      | 'KeyEncipherment'
      | 'NonRepudiation'
      | 'DigitalSignature'
      | 'DecipherOnly';
    X509SubjectAlternativeNameExtension: components['schemas']['X509Extension'];
    X509SubjectKeyIdentifierExtension: WithRequired<
      {
        subjectKeyIdentifier: string | null;
        subjectKeyIdentifierBytes: components['schemas']['ByteReadOnlyMemory'];
      } & components['schemas']['X509Extension'],
      'subjectKeyIdentifier' | 'subjectKeyIdentifierBytes'
    >;
    __ComObject: components['schemas']['MarshalByRefObject'];
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}

export type external = Record<string, never>;

export type operations = Record<string, never>;
